/**
 * 设置处理程序
 * 负责应用设置的加载和保存
 */

import { ipcMain, app } from 'electron';
import { store } from '../config/store-config.js';

/**
 * 注册设置相关的IPC处理程序
 */
export function registerSettingsHandlers() {
  // 加载设置
  ipcMain.handle('load-settings', async () => {
    try {
      // 从 electron-store 获取所有设置
      const settings = store.store; // .store 属性包含所有数据
      console.log('Loading settings:', settings);
      return { success: true, data: settings };
    } catch (error) {
      console.error('Failed to load settings:', error);
      return { success: false, error: '无法加载设置' };
    }
  });

  // 保存设置
  ipcMain.handle('save-settings', async (_event, settingsData) => {
    try {
      // 将接收到的设置数据直接保存到 electron-store
      store.set(settingsData);
      console.log('Settings saved:', settingsData);
      return { success: true };
    } catch (error) {
      console.error('Failed to save settings:', error);
      return { success: false, error: '无法保存设置' };
    }
  });

  // 获取应用版本号
  ipcMain.handle('get-app-version', (_event) => {
    return app.getVersion();
  });
} 