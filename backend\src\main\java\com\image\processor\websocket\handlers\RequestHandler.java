package com.image.processor.websocket.handlers;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * WebSocket请求处理器接口
 * 所有具体的处理器都需实现此接口
 */
public interface RequestHandler {
    /**
     * 处理WebSocket请求
     * 
     * @param conn WebSocket连接
     * @param request 请求JSON对象
     * @throws JSONException 如果JSON解析错误
     */
    void handle(WebSocket conn, JSONObject request) throws JSONException;
    
    /**
     * 获取此处理器支持的action名称
     * 
     * @return 支持的action字符串
     */
    String getActionName();
} 