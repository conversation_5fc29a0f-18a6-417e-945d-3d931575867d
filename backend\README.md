# 文档图像处理系统

## 项目结构

```
.
├── backend/                 # 后端服务
│   ├── src/                # 源代码
│   │   ├── main/          # 主程序
│   │   │   ├── java/     # Java源代码
│   │   │   └── resources/# 资源文件
│   │   └── test/         # 测试代码
│   ├── target/            # 编译输出
│   └── pom.xml            # Maven配置
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   └── package.json       # 依赖配置
└── README.md              # 项目说明
```

## 缩略图生成与文件夹扫描流程

### 1. 缩略图命名规则
- 使用原文件名加 `_thumb` 后缀
- 保持原文件扩展名
- 示例：`image.jpg` -> `image_thumb.jpg`

### 2. 缩略图存储路径
- 默认存储在用户数据目录下的 `thumbnails` 文件夹
- 可在设置中自定义存储路径
- 路径格式：`{userDataPath}/thumbnails/{relativePath}/{filename}_thumb.{ext}`

### 3. 文件夹扫描流程
1. 前台选择文件夹
2. 发送整个文件夹路径到后台
3. 后台递归扫描所有图片文件
4. 为每个图片生成缩略图
5. 实时返回进度和结果

### 4. 图片显示逻辑
- 优先使用缩略图
- 如果缩略图不存在，显示原图
- 缩略图生成过程中显示加载状态

### 5. 性能优化
- 使用线程池处理缩略图生成
- 支持批量处理
- 缓存已生成的缩略图
- 支持任务取消

## 开发环境设置

### 后端开发环境
1. 安装 JDK 8 或更高版本（推荐JDK 8，兼容老旧Linux系统）
2. 安装 Maven 3.6 或更高版本
3. 安装 OpenCV 和 JavaCV 依赖

### 前端开发环境
1. 安装 Node.js 16 或更高版本
2. 安装 Vue CLI
3. 安装 Electron

## 构建和运行

### 后端服务
```bash
cd backend
mvn clean package
java -jar target/docimage-processor-1.0-SNAPSHOT.jar
```

### 前端应用
```bash
cd frontend
npm install
npm run electron:serve  # 开发模式
npm run electron:build  # 生产构建
```

## 配置说明

### 后端配置
- 端口配置：默认 11000
- 备用端口：11001, 11002
- 线程池大小：CPU核心数
- 缓存大小：1000

### 前端配置
- 缩略图存储路径
- 输出模式（替换/新文件/新文件夹）
- 默认图像格式
- GPU加速选项

## 通信协议

### WebSocket 消息格式
```json
{
  "action": "generate_thumbnail",
  "taskId": "uuid",
  "inputPath": "文件路径",
  "outputPath": "输出路径",
  "width": 150,
  "height": 150
}
```

### 进度更新格式
```json
{
  "taskId": "uuid",
  "status": "progress",
  "progress": 50,
  "message": "处理中..."
}
```

## 错误处理
- 文件不存在
- 格式不支持
- 处理失败
- 连接中断

## 许可证
MIT 