/**
 * 后端服务管理
 * 负责启动、重启和管理Java后端服务
 */

import { spawn } from 'node:child_process';
import path from 'node:path';
import { app } from 'electron';
import { BACKEND_CONFIG } from '../config/app-config.js';

// 后端进程引用
let backendProcess = null;
let reconnectAttempts = 0;

/**
 * 获取应用可执行文件路径
 * @returns {string} 应用可执行文件路径
 */
function getAppExePath() {
  if (app) {
    try {
      return app.getPath('exe');
    } catch (e) {
      return process.execPath;
    }
  }
  return process.execPath;
}

/**
 * 重启后端服务
 * @returns {boolean} 是否成功启动
 */
export function restartBackendService() {
  // 如果设置为不自动启动后端，则直接返回
  if (!BACKEND_CONFIG.AUTO_START) {
    console.log('开发模式：跳过自动启动后端服务');
    return false;
  }

  console.log('尝试重启后端服务...');
  
  // 如果已有进程，先尝试终止
  if (backendProcess !== null) {
    try {
      process.kill(backendProcess.pid);
    } catch (error) {
      console.log('终止旧进程时出错:', error);
    }
  }
  
  // 启动新的后端进程
  try {
    // 使用javaw避免命令窗口，启动后端JAR
    const javaArgs = [
      ...BACKEND_CONFIG.JVM_OPTS,
      '-jar',
      path.join(path.dirname(getAppExePath()), 'backend/image-processor.jar')
    ];

    console.log('启动后端命令:', 'javaw', javaArgs.join(' '));
    
    backendProcess = spawn('javaw', javaArgs, {
      detached: true, // 分离子进程
      stdio: 'ignore' // 忽略标准I/O
    });
    
    backendProcess.on('error', (err) => {
      console.error('后端进程启动失败:', err);
    });
    
    // 重置重连计数
    reconnectAttempts = 0;
    
    console.log('后端服务启动中，等待连接...');
    return true;
  } catch (error) {
    console.error('启动后端服务出错:', error);
    return false;
  }
}

/**
 * 增加重连尝试次数
 * @returns {boolean} 是否超过最大尝试次数
 */
export function incrementReconnectAttempts() {
  reconnectAttempts++;
  return reconnectAttempts >= BACKEND_CONFIG.MAX_RECONNECT_ATTEMPTS;
}

/**
 * 重置重连尝试次数
 */
export function resetReconnectAttempts() {
  reconnectAttempts = 0;
}

export { BACKEND_CONFIG }; 