/**
 * 存储配置
 * 初始化和管理electron-store
 */

import Store from 'electron-store';
import { defaultSettings } from './app-config.js';

// 初始化 electron-store
export const store = new Store();

/**
 * 初始化应用设置
 * 确保默认设置存在，并合并缺失的键
 */
export function initializeSettings() {
  const currentSettings = store.store;
  let settingsChanged = false;

  // 检查并合并 basic 设置
  if (!currentSettings.basic) {
    store.set('basic', defaultSettings.basic);
    settingsChanged = true;
  } else {
    const mergedBasic = { ...defaultSettings.basic, ...currentSettings.basic };
    // 检查是否有变化，避免不必要的写入
    if (JSON.stringify(mergedBasic) !== JSON.stringify(currentSettings.basic)) {
      store.set('basic', mergedBasic);
      settingsChanged = true;
    }
  }

  // 检查并合并 performance 设置
  if (!currentSettings.performance) {
    store.set('performance', defaultSettings.performance);
    settingsChanged = true;
  } else {
    const mergedPerformance = { ...defaultSettings.performance, ...currentSettings.performance };
    if (JSON.stringify(mergedPerformance) !== JSON.stringify(currentSettings.performance)) {
      store.set('performance', mergedPerformance);
      settingsChanged = true;
    }
  }

  if (settingsChanged) {
    console.log('设置已初始化或已更新为默认值。');
  } else if (Object.keys(currentSettings).length === 0) {
    console.log('首次初始化默认设置。'); 
    store.set({
      basic: defaultSettings.basic,
      performance: defaultSettings.performance
    });
  }
} 