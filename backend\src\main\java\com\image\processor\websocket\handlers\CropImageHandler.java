package com.image.processor.websocket.handlers;

import com.image.processor.core.CropParams;
import com.image.processor.core.ImageProcessorService;
import com.image.processor.core.ImageProcessorService.ProcessingResult;
import com.image.processor.core.ProgressCallback;
import com.image.processor.websocket.TaskInfo;
import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 裁剪图片请求处理器
 */
public class CropImageHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;

    public CropImageHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "crop_image";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        final String taskId = request.getString("taskId");
        final String baselineImagePath = request.has("baselineImagePath") ? request.getString("baselineImagePath") : null;
        final String inputPath =request.has("inputPath") ? request.getString("inputPath") : null;
        final JSONObject cropJson = request.getJSONObject("params").getJSONObject("crop");
        final String outputPath = request.has("outputPath") ? request.getString("outputPath") : null;

        logger.info("收到裁剪图片请求: taskId={}, 输入={}, 基准图={}, 裁剪参数={}", taskId, inputPath,baselineImagePath, cropJson.toString());

        sendTaskUpdate(conn, taskId, "accepted", 0, "任务已接受，准备处理");
        TaskInfo taskInfo = new TaskInfo(taskId, conn);
        activeTasks.put(taskId, taskInfo);

        taskExecutor.submit(() -> {
            try {
                if (taskInfo.isCancelled()) {
                    logger.info("任务已被取消，不执行: {}", taskId);
                    return;
                }
                sendTaskUpdate(conn, taskId, "started", 0, "开始裁剪图片");
                ImageProcessorService processor = ImageProcessorService.getInstance();

                CropParams cropParams = new CropParams(
                        cropJson.getInt("x"),
                        cropJson.getInt("y"),
                        cropJson.getInt("width"),
                        cropJson.getInt("height")
                );

                ProgressCallback progressCallback = (progress, message) -> {
                    if (taskInfo.isCancelled()) return false;
                    sendTaskUpdate(conn, taskId, "progress", progress, message);
                    return true;
                };

                ProcessingResult result = processor.applyCrop(inputPath, cropParams, progressCallback,taskId,true);

                if (taskInfo.isCancelled()) {
                    logger.info("任务在处理过程中被取消: {}", taskId);
                    sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
                    if (result != null) result.release();
                    return;
                }

                if (result != null) {
                    String resultPath = null;
                    if (outputPath != null) {
                        resultPath = processor.saveProcessedImage(result, outputPath);
                    }
                    JSONObject resultData = new JSONObject();
                    resultData.put("taskId", taskId);
                    resultData.put("resultPath", resultPath);
                    sendTaskUpdate(conn, taskId, "completed", 100, resultData);
                    logger.info("裁剪图片请求已处理: taskId={}", taskId);
                    result.release();
                } else {
                    sendTaskUpdate(conn, taskId, "error", 0, "裁剪失败");
                }
            } catch (Exception e) {
                logger.error("执行裁剪任务时出错: taskId=" + taskId, e);
                sendTaskUpdate(conn, taskId, "error", 0, "处理出错: " + e.getMessage());
            } finally {
                activeTasks.remove(taskId);
            }
        });
    }
} 