{"name": "docimage-processor-frontend", "version": "0.1.0", "description": "文档图像处理工具前端", "main": "main.js", "type": "module", "scripts": {"start": "electron .", "dev": "vite", "start-backend": "cd ../backend && mvn clean compile exec:java", "build": "vite build", "electron:dev": "concurrently \"npm run dev\" \"npm run start-backend\" \"electron .\"", "electron:build": "vite build && electron-builder"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "electron-store": "^8.1.0", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5", "ws": "^8.18.1"}, "devDependencies": {"@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "concurrently": "^8.2.2", "electron": "35.2.0", "electron-builder": "26.0.12", "vite": "^5.0.10"}}