"use strict";
const { contextBridge, ipcRenderer } = require('electron');

// 白名单渲染进程能调用的IPC通道
const validChannels = [
    'open-directory',
    'selected-directory',
    'generate-thumbnail',
    'scan-directory',
    'apply-image-edit',
    'preview-image-edit',
    'get-image-info',
    'get-temp-path',
    'save-history',
    'load-history',
    'get-cached-image',
    'process-image',
    'batch-process',
    'list-images',
    'detect-boundaries',
    'ai-enhance',
    'calculate-thumbnail-path',
    'load-image-history',
    'save-image-history',
    'load-settings',
    'save-settings',
    'start-folder-scan',
    'get-thumbnail-path',
    'load-image-params',
    'process-module',
    'basic_adjust_params',
    'set-baseline-image',
    'get-cached-image-apply-params',
];

// 暴露IPC通信方法给渲染进程
contextBridge.exposeInMainWorld('electron', {
    // 获取应用版本
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    // 选择文件
    selectFiles: () => ipcRenderer.invoke('select-files'),
    // 选择文件夹
    selectDirectory: async () => {
        try {
            const result = await ipcRenderer.invoke('select-directory');
            return result;
        } catch (error) {
            console.error('选择文件夹失败:', error);
            return { success: false, error: error.message };
        }
    },

    // --- 新增: 设置读写接口 ---
    loadSettings: () => ipcRenderer.invoke('load-settings'),
    saveSettings: (settingsData) => ipcRenderer.invoke('save-settings', settingsData),
    // generateThumbnail: (filePath) => ipcRenderer.invoke('generate-thumbnail', filePath),
    startFolderScan: (directoryPath) => ipcRenderer.invoke('start-folder-scan', directoryPath),
    // -------------------------

    // IPC 通信方法 (通用)
    invoke: (channel, data) => {
        if (validChannels.includes(channel)) {
            return ipcRenderer.invoke(channel, data);
        } else {
            console.error(`Attempted to invoke invalid channel: ${channel}`);
            return Promise.reject(new Error(`无效的通信通道: ${channel}`));
        }
    },
    // 监听IPC响应
    onResponse: (callback) => {
        ipcRenderer.on('ipc-response', (event, ...args) => callback(...args));
        return () => ipcRenderer.removeListener('ipc-response', callback);
    },
    // 监听进度更新
    onProgress: (callback) => {
        ipcRenderer.on('ipc-progress', (event, ...args) => callback(...args));
        return () => ipcRenderer.removeListener('ipc-progress', callback);
    },
    // 监听文件夹扫描完成
    onFolderScanComplete: (callback) => {
        const listener = (event, results) => callback(results);
        ipcRenderer.on('folder-scan-complete', listener);
        // 返回一个取消监听的函数
        return () => ipcRenderer.removeListener('folder-scan-complete', listener);
    },
    // 新增：监听预览更新
    onPreviewUpdate: (callback) => {
        const listener = (event, data) => callback(data);
        ipcRenderer.on('preview-update', listener);
        // 返回一个取消监听的函数
        return () => {
            console.log('Removing preview-update listener');
            ipcRenderer.removeListener('preview-update', listener);
        };
    }
}); 