package com.image.processor.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.image.processor.websocket.ImageProcessorServer;

/**
 * 主应用程序入口类，负责启动WebSocket服务器
 */
public class ServiceMain {
    private static final Logger logger = LoggerFactory.getLogger(ServiceMain.class);
    
    // 默认端口列表，从10000开始，预留3个端口
    private static final List<Integer> DEFAULT_PORTS = Arrays.asList(11000, 21001, 31002);
    
    public static void main(String[] args) {
        // 解析命令行参数，获取端口号
        List<Integer> ports = DEFAULT_PORTS;
        if (args.length > 0) {
            try {
                // 如果提供了端口号，则只使用该端口
                ports = Arrays.asList(Integer.parseInt(args[0]));
            } catch (NumberFormatException e) {
                logger.warn("无效的端口号：{}，使用默认端口列表：{}", args[0], DEFAULT_PORTS);
            }
        }

        // 尝试启动服务器，如果端口被占用则尝试下一个端口
        ImageProcessorServer server = null;
        int usedPort = -1;
        
        for (int port : ports) {
            try {
                server = new ImageProcessorServer(new InetSocketAddress(port));
                server.setReuseAddr(true);
                server.start();
                usedPort = port;
                logger.info("图像处理服务已启动，监听端口: {}", port);
                System.out.println("图像处理服务已启动，监听端口: " + port);
                break;
            } catch (Exception e) {
                logger.warn("端口 {} 被占用，尝试下一个端口", port);
                if (server != null) {
                    try {
                        server.stop();
                    } catch (Exception ex) {
                        logger.error("停止服务器时出错", ex);
                    }
                }
            }
        }
        
        if (usedPort == -1) {
            logger.error("所有端口都被占用，无法启动服务");
            System.err.println("所有端口都被占用，无法启动服务");
            return;
        }
        
        System.out.println("按下回车键停止服务...");

        // 等待回车键退出
        try {
            new BufferedReader(new InputStreamReader(System.in)).readLine();
            server.stop();
            logger.info("图像处理服务已停止");
            System.out.println("图像处理服务已停止");
        } catch (Exception e) {
            logger.error("服务停止时发生错误: {}", e.getMessage(), e);
            System.err.println("服务停止时发生错误: " + e.getMessage());
        }
    }
} 