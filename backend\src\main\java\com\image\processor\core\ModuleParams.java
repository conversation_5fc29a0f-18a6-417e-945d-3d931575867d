package com.image.processor.core;

/**
 * 模块参数接口
 * 所有功能模块的参数类都应实现此接口
 */
public interface ModuleParams {
    
    /**
     * 检查参数是否为空（全为默认值）
     * 
     * @return 如果所有参数都是默认值返回true，否则返回false
     */
    boolean isEmpty();
    
    /**
     * 获取参数类型标识
     * 
     * @return 参数类型标识字符串
     */
    String getParamType();
    
    /**
     * 将参数转换为JSON字符串，用于存储和传输
     * 
     * @return JSON格式的参数字符串
     */
    String toJsonString();
    
    /**
     * 从JSON字符串加载参数
     * 
     * @param jsonString JSON格式的参数字符串
     * @return 加载成功返回true，否则返回false
     */
    boolean fromJsonString(String jsonString);
} 