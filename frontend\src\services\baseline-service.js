/**
 * 图像处理服务
 * 负责处理图像的加载、显示和编辑流程
 */

import { ElMessage } from 'element-plus';
import { shallowRef } from 'vue';
import TaskIdUtils from '../utils/TaskIdUtils.js';

class ImageProcessService {
  constructor() {
    // 图像路径状态
    this.originalPath = null;        // 原图路径
    this.thumbnailPath = null;       // 缩略图路径
    
    // 任务ID状态
    this.taskId = null;              // 主任务ID
    this.baselineTaskId = null;      // 基准图任务ID
    this.useImageTaskId = null;      // 使用图任务ID
    
    // 模块参数状态
    this.allModuleParams = {};       // 所有模块的参数
    this.currentModuleId = null;     // 当前激活的模块ID
    
    // 图像路径状态
    this.baselineImagePath = null;   // 基准图路径
    this.useImagePath = null;        // 使用图路径
    
    // 显示状态
    this.displayUrl = '';            // 当前显示的图像URL
    
    // 监听器
    this.previewUpdateListener = null; // 预览更新监听器
  }

  /**
   * 加载图片，
   * @param {string} filePath 图像文件路径
   * @param {string} thumbnailPath 缩略图路径
   * @param {string} moduleId 当前模块ID
   * @param {Function} onImageLoaded 图像加载完成回调
   * @returns {Promise<Object>} 加载结果
   */
  async loadImage(filePath, thumbnailPath, moduleId, onImageLoaded = null) {
    console.log(`加载图像: ${filePath}, 缩略图: ${thumbnailPath}, 模块: ${moduleId}`);
    // 1.计算当前taskid
    const taskId = await TaskIdUtils.createTaskId(filePath);
    console.log(`计算得到taskId: ${taskId}`);
    this.taskId = taskId;
    this.originalPath = filePath;
    if(!thumbnailPath){
      const getThumbnailPathResult = await window.electron.invoke('get-thumbnail-path', {
        filePath: filePath,
      });
      this.thumbnailPath=getThumbnailPathResult.thumbnailPath
      thumbnailPath=getThumbnailPathResult.thumbnailPath
    }
    // 2.计算当前模块id
    this.currentModuleId = moduleId || 'default';
    console.log(`当前模块ID: ${this.currentModuleId}`);
    
    // 3.计算当前模块参数
    // 直接从后端加载历史参数
    const params = await this.loadHistoryParams(filePath);
    this.allModuleParams = params || {};


    // 获取当前模块参数
    const currentModuleParams = this.allModuleParams[this.currentModuleId] || {};
    console.log(`当前模块参数:`, currentModuleParams);
    
    // 4.计算所有参数除了当前模块
    const otherModuleParams = this.getOtherModuleParams();
    console.log(`其他模块参数:`, otherModuleParams);
    
    // 保存所有参数
    this.allModuleParams = {
      ...otherModuleParams,
      [this.currentModuleId]: currentModuleParams
    };
    
    // 5.发送模块处理请求
    console.log('发送模块处理请求:', {
      originalPath: filePath,
      thumbnailPath: thumbnailPath,
      taskId: taskId,
      moduleId: this.currentModuleId,
      currentModuleParams: currentModuleParams,
      otherModuleParams: otherModuleParams
    });
    
    const processResult = await window.electron.invoke('process-module', {
      originalPath: filePath,
      thumbnailPath: thumbnailPath,
      taskId: taskId,
      moduleId: this.currentModuleId,
      currentModuleParams: currentModuleParams,
      otherModuleParams: otherModuleParams
    });

    // 6.获取处理后的图像
    if (!processResult || !processResult.success) {
      const errorMsg = processResult?.error || '模块处理失败：未知错误';
      console.error('模块处理失败:', errorMsg);
      ElMessage.error(`模块处理失败: ${errorMsg}`);
      throw new Error(errorMsg);
    }
    
    console.log('模块处理成功:', processResult);
    
    // 更新状态
    if (processResult.taskId) this.taskId = processResult.taskId;
    
    // 7.显示处理后的图像
    if (processResult.imagePath) {
      this.displayUrl = `file://${processResult.imagePath}?t=${Date.now()}`;
      console.log(`显示图像: ${this.displayUrl}`);
    } else {
      const errorMsg = '处理成功但未返回图像路径';
      console.error(errorMsg);
      ElMessage.error(errorMsg);
      throw new Error(errorMsg);
    }
    
    // 通知图像已加载
    if (onImageLoaded) {
      onImageLoaded(this.displayUrl);
    }
    
    return {
      success: true,
      taskId: this.taskId,
      displayUrl: this.displayUrl,
      moduleId: this.currentModuleId
    };
  }

  /**
   * 设置当前模块ID
   * @param {string} moduleId 模块ID
   */
  setCurrentModule(moduleId) {
    this.currentModuleId = moduleId;
    console.log(`设置当前模块为: ${moduleId}`);
  }

  /**
   * 更新模块参数
   * @param {string} moduleId 模块ID
   * @param {Object} params 模块参数
   */
  updateModuleParams(moduleId, params) {
    this.allModuleParams[moduleId] = { ...params };
    console.log(`更新模块 ${moduleId} 参数:`, params);
  }

  /**
   * 获取除当前模块外的所有参数
   * @returns {Object} 除当前模块外的所有参数
   */
  getOtherModuleParams() {
    const otherParams = {};
    
    // 复制所有参数但排除当前模块
    for (const moduleId in this.allModuleParams) {
      if (moduleId !== this.currentModuleId) {
        otherParams[moduleId] = { ...this.allModuleParams[moduleId] };
      }
    }
    
    return otherParams;
  }
  
  /**
   * 获取当前模块参数
   * @returns {Object} 当前模块参数
   */
  getCurrentModuleParams() {
    if (!this.currentModuleId || !this.allModuleParams[this.currentModuleId]) {
      return {};
    }
    
    return { ...this.allModuleParams[this.currentModuleId] };
  }

  /**
   * 发送模块处理请求
   * @param {string} moduleId 当前模块ID
   * @param {Object} params 当前模块参数
   * @param {Function} onTaskIdGenerated 任务ID生成回调
   * @param {Function} onImageUpdated 图像更新回调
   * @returns {Promise<Object>} 处理结果
   */
  async sendModuleProcessRequest(moduleId, params, onTaskIdGenerated = null, onImageUpdated = null) {
    if (!window.electron || !this.originalPath || !this.taskId) {
      throw new Error('无法发送模块处理请求：缺少必要条件');
    }
    
    // 更新当前模块和参数
    this.setCurrentModule(moduleId);
    this.updateModuleParams(moduleId, params);
    
    // 获取其他模块参数
    const otherModuleParams = this.getOtherModuleParams();
    
    try {
      console.log('发送模块处理请求:', {
        originalPath: this.originalPath,
        thumbnailPath: this.thumbnailPath,
        taskId: this.taskId,
        moduleId: this.currentModuleId,
        otherModuleParams: otherModuleParams,
        currentModuleParams: params
      });
      
      // 调用后端API处理模块请求
      const result = await window.electron.invoke('process-module', {
        originalPath: this.originalPath,
        thumbnailPath: this.thumbnailPath,
        taskId: this.taskId,
        moduleId: this.currentModuleId,
        otherModuleParams: otherModuleParams,
        currentModuleParams: params
      });
      
      console.log('模块处理响应:', result);
      
      if (!result || !result.success) {
        throw new Error(result?.error || '模块处理请求失败：未知错误');
      }
      
      // 更新任务ID
      if (result.taskId) this.taskId = result.taskId;
      
      // 通知任务ID生成
      if (onTaskIdGenerated && result.taskId) {
        onTaskIdGenerated(result.taskId);
      }
      
      // 获取并显示处理后的图像
      if (result.imagePath) {
        this.displayUrl = `file://${result.imagePath}?t=${Date.now()}`;
        
        // 通知图像已更新
        if (onImageUpdated) {
          onImageUpdated(this.displayUrl);
        }
      } else {
        throw new Error('处理成功但未返回图像路径');
      }
      
      return {
        success: true,
        taskId: this.taskId,
        displayUrl: this.displayUrl
      };
    } catch (error) {
      console.error('发送模块处理请求出错:', error.message);
      ElMessage.error(`模块处理请求出错: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取并显示图像
   * @param {string} taskId 任务ID
   * @param {Function} onImageUpdated 图像更新回调
   * @returns {Promise<boolean>} 是否成功
   */
  async fetchAndDisplayImage(taskId, onImageUpdated = null) {
    if (!window.electron || !taskId) return false;
    
    try {
      console.log(`获取图像数据，任务ID: ${taskId}`);
      
      // 首先尝试获取图像路径
      const pathResult = await window.electron.invoke('get-image-path', {
        taskId: taskId
      });
      
      if (pathResult && pathResult.success && pathResult.imagePath) {
        // 使用文件路径显示图像
        this.displayUrl = `file://${pathResult.imagePath}?t=${Date.now()}`;
        console.log(`使用文件路径显示图像: ${this.displayUrl}`);
        
        if (onImageUpdated) {
          onImageUpdated(this.displayUrl);
        }
        
        return true;
      }
      
      // 如果无法获取路径，尝试获取图像数据
      const imageResult = await window.electron.invoke('get-cached-image', {
        taskId: taskId,
        quality: 90
      });
      
      if (imageResult && imageResult.success && imageResult.imageData) {
        // 使用Base64显示图像
        this.displayUrl = `data:image/jpeg;base64,${imageResult.imageData}`;
        console.log('使用Base64数据显示图像');
        
        if (onImageUpdated) {
          onImageUpdated(this.displayUrl);
        }
        
        return true;
      }
      
      console.error('无法获取图像数据:', imageResult?.error || '未知错误');
      return false;
    } catch (error) {
      console.error('获取图像数据出错:', error);
      return false;
    }
  }

  /**
   * 处理预览更新
   * @param {Object} result 预览结果
   * @param {Function} onImageLoaded 图像加载完成回调
   */
  handlePreviewUpdate(result, onImageLoaded = null) {
    console.log('收到预览更新:', result);
    
    if (!result || !result.success) {
      console.error('预览处理失败:', result?.error);
      ElMessage.error(`预览处理失败: ${result?.error || '未知错误'}`);
      return;
    }
    
    // 更新任务ID状态
    if (result.taskId) this.taskId = result.taskId;
    
    // 显示图像
    if (result.imagePath) {
      this.displayUrl = `file://${result.imagePath}?t=${Date.now()}`;
      console.log(`预览更新成功，显示图像: ${result.imagePath}`);
      
      // 通知图像已更新
      if (onImageLoaded) {
        onImageLoaded(this.displayUrl);
      }
    } else {
      console.error('预览更新没有返回图像路径');
      return;
    }
  }

  /**
   * 设置预览更新监听器
   * @param {Function} onImageLoaded 图像加载完成回调
   * @returns {Function} 移除监听器的函数
   */
  setupPreviewUpdateListener(onImageLoaded) {
    // 如果已有监听器，先移除
    if (this.previewUpdateListener) {
      this.previewUpdateListener();
      this.previewUpdateListener = null;
    }
    
    // 监听来自主进程的预览更新
    if (window.electron && window.electron.onPreviewUpdate) {
      const handleUpdate = (result) => this.handlePreviewUpdate(result, onImageLoaded);
      this.previewUpdateListener = window.electron.onPreviewUpdate(handleUpdate);
      console.log('已设置预览更新监听器');
      return this.previewUpdateListener;
    } else {
      console.error('无法设置预览更新监听器: window.electron.onPreviewUpdate 不可用');
      return null;
    }
  }

  /**
   * 移除预览更新监听器
   */
  removePreviewUpdateListener() {
    if (this.previewUpdateListener) {
      this.previewUpdateListener();
      this.previewUpdateListener = null;
      console.log('已移除预览更新监听器');
    }
  }
  
  /**
   * 保存模块参数到历史记录
   * @returns {Promise<boolean>} 是否成功
   */
  async saveModuleParamsToHistory() {
    if (!window.electron || !this.originalPath) return false;
    
    try {
      console.log('保存模块参数到历史记录:', {
        originalPath: this.originalPath,
        params: this.allModuleParams
      });
      
      const result = await window.electron.invoke('save-image-params', {
        filePath: this.originalPath,
        params: this.allModuleParams
      });
      
      if (result && result.success) {
        console.log('保存参数成功');
        return true;
      } else {
        console.error('保存参数失败:', result?.error || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('保存参数出错:', error);
      return false;
    }
  }

  /**
   * 加载历史参数
   * @param {string} filePath 图像文件路径
   * @returns {Promise<Object>} 历史参数
   */
  async loadHistoryParams(filePath) {
    if (!window.electron || !filePath) return null;
    
    try {
      console.log('加载历史参数:', filePath);
      
      const result = await window.electron.invoke('load-image-params', {
        filePath: filePath
      });
      
      if (result && result.success && result.params) {
        console.log('加载历史参数成功:', result.params);
        return result.params;
      } else {
        console.log('无历史参数或加载失败');
        return null;
      }
    } catch (error) {
      console.error('加载历史参数出错:', error);
      return null;
    }
  }

  /**
   * 生成会话ID
   * @param {string} imagePath 图像路径
   * @returns {string} 会话ID
   */
  generateSessionId(imagePath) {
    if (!imagePath) return 'default-session';
    
    // 使用简单的字符串哈希
    let hash = 0;
    for (let i = 0; i < imagePath.length; i++) {
      const char = imagePath.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `session_${Math.abs(hash).toString(16)}`;
  }

  /**
   * 获取滤镜字符串
   * @param {Object} params 编辑参数
   * @returns {string} CSS滤镜字符串
   */
  getFilterString(params) {
    if (!params) return '';
    
    // 从params中提取亮度、对比度、饱和度和锐度参数
    const brightness = params.brightness || 0;
    const contrast = params.contrast || 0;
    const saturation = params.saturation || 0;
    const sharpness = params.sharpness || 0;
    
    return `
      brightness(${100 + brightness}%)
      contrast(${100 + contrast}%)
      saturate(${100 + saturation}%)
      ${sharpness > 0 ? `contrast(${100 + sharpness * 0.5}%)` : ''}
    `;
  }
  
  /**
   * 获取当前图像状态
   * @returns {Object} 图像状态
   */
  getImageState() {
    return {
      originalPath: this.originalPath,
      thumbnailPath: this.thumbnailPath,
      taskId: this.taskId,
      displayUrl: this.displayUrl,
      currentModuleId: this.currentModuleId
    };
  }
}

// 导出单例实例
export default new ImageProcessService(); 