/**
 * 缩略图服务
 * 负责处理缩略图的生成和管理
 */

import { promises as fs } from 'node:fs';
import fs_sync from 'node:fs';
import path from 'node:path';
import crypto from 'node:crypto';
import WebSocket from 'ws';
import { store } from '../config/store-config.js';
import { 
  connectToWebSocket, 
  generateTaskId, 
  setTaskCallback, 
  isConnected, 
  sendWebSocketMessage 
} from './websocket-service.js';

/**
 * 计算缩略图路径
 * @param {string} filePath 原始文件路径
 * @returns {Promise<Object>} 计算结果
 */
export async function calculateThumbnailPath(filePath) {
  if (!filePath) {
    return { success: false, error: '未指定文件路径' };
  }
  
  try {
    // 从设置中获取缩略图路径
    const settings = store.get('basic') || {};
    const thumbnailDir = settings.thumbnailPath;
    const scanDirectory = settings.scanDirectory || '';
    
    if (!thumbnailDir) {
      return { success: false, error: '未配置缩略图目录' };
    }
    
    // 读取文件内容计算SHA1
    const fileBuffer = await fs.readFile(filePath);
    const hash = crypto.createHash('sha1');
    hash.update(fileBuffer);
    const fileHash = hash.digest('hex');
    
    // 构建缩略图路径，保留包括扫描目录名本身在内的完整路径
    let relativePath = '';
    
    if (scanDirectory && filePath.startsWith(scanDirectory)) {
      // 如果有扫描目录并且文件在其中，获取相对路径
      // 获取扫描目录的名称
      const scanDirName = path.basename(scanDirectory);
      
      // 计算文件相对于扫描目录父级的路径，这样可以包含扫描目录名
      const scanParent = path.dirname(scanDirectory);
      relativePath = path.relative(scanParent, path.dirname(filePath));
      console.log(`计算相对路径: 扫描目录=${scanDirectory}, 扫描父级=${scanParent}, 文件目录=${path.dirname(filePath)}, 相对路径=${relativePath}`);
    } else {
      // 如果没有扫描目录或文件不在扫描目录中，使用最后两级目录
      const dirParts = path.dirname(filePath).split(path.sep);
      if (dirParts.length > 1) {
        // 只使用最后两级目录（如果有的话）
        relativePath = path.join(dirParts[dirParts.length - 2] || '', dirParts[dirParts.length - 1] || '');
      } else {
        // 只有一级目录
        relativePath = dirParts[0] || '';
      }
      console.log(`使用最后两级目录: 文件路径=${filePath}, 相对路径=${relativePath}`);
    }
    
    const ext = path.extname(filePath);
    const thumbnailFileName = `${fileHash}_thumb${ext}`;
    const thumbnailFilePath = path.join(thumbnailDir, relativePath, thumbnailFileName);
    
    // 检查缩略图是否存在
    const exists = fs_sync.existsSync(thumbnailFilePath);
    console.log(`缩略图完整路径: ${thumbnailFilePath}, 是否存在: ${exists}`);
    
    return { 
      success: true, 
      thumbnailPath: thumbnailFilePath,
      thumbnailExists: exists,
      fileHash: fileHash,
      originalPath: filePath
    };
  } catch (error) {
    console.error('计算缩略图路径失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 生成缩略图
 * @param {string} filePath 原始文件路径
 * @param {Object} options 选项
 * @returns {Promise<Object>} 生成结果
 */
export async function generateThumbnail(filePath, options = {}) {
  if (!filePath) {
    return { success: false, error: '未指定文件路径' };
  }
  
  const settings = store.get('basic') || {};
  const thumbnailDir = settings.thumbnailPath;
  
  if (!thumbnailDir) {
    return { success: false, error: '未配置缩略图存储路径' };
  }

  try {
    // 计算缩略图路径
    const pathResult = await calculateThumbnailPath(filePath);
    
    if (!pathResult.success) {
      return pathResult;
    }
    
    const thumbnailFilePath = pathResult.thumbnailPath;
    
    // 检查缩略图是否已存在
    if (pathResult.thumbnailExists) {
      return { 
        success: true, 
        thumbnailPath: thumbnailFilePath,
        fileHash: pathResult.fileHash
      };
    }
    
    // 确保存储目录存在
    const dirPath = path.dirname(thumbnailFilePath);
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (mkdirError) {
      console.error(`创建缩略图目录失败: ${dirPath}`, mkdirError);
      return { success: false, error: '无法创建缩略图目录' };
    }
    
    // 确保WebSocket已连接
    if (!isConnected()) {
      await connectToWebSocket();
      if (!isConnected()) {
        return { success: false, error: 'WebSocket服务未连接' };
      }
    }
    
    // 创建Promise以等待缩略图生成完成
    return new Promise((resolve) => {
      const taskId = generateTaskId();
      
      const width = options.width || 1200;
      const height = options.height || 1200;
      const quality = options.quality || 85;
      
      // 设置任务回调
      setTaskCallback(taskId, {
        resolve: (data) => {
          resolve({ 
            success: true, 
            thumbnailPath: thumbnailFilePath,
            fileHash: pathResult.fileHash
          });
        },
        reject: (error) => {
          resolve({ success: false, error: error.message });
        }
      });
      
      // 发送WebSocket请求
      const request = {
        action: 'generate_thumbnail',
        taskId: taskId,
        inputPath: filePath,
        outputPath: thumbnailFilePath,
        width: width,
        height: height,
        quality: quality,
        keepAspectRatio: options.keepAspectRatio !== false,
        resizeMode: options.resizeMode || 'fit'
      };
      
      sendWebSocketMessage(request);
      
      // 设置超时处理
      setTimeout(() => {
        const callback = setTaskCallback.get(taskId);
        if (callback) {
          resolve({ success: false, error: '缩略图生成超时' });
          setTaskCallback.delete(taskId);
        }
      }, 30000); // 30秒超时
    });
  } catch (error) {
    console.error('生成缩略图失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 查找图像文件
 * @param {string} dir 目录路径
 * @param {Array} allFiles 累积的文件列表
 * @returns {Promise<Array>} 图像文件列表
 */
export async function findImageFiles(dir, allFiles = []) {
  const files = await fs.readdir(dir);
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = await fs.stat(fullPath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      await findImageFiles(fullPath, allFiles);
    } else if (stat.isFile()) {
      // 检查文件扩展名
      const ext = path.extname(file).toLowerCase();
      if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'].includes(ext)) {
        allFiles.push(fullPath);
      }
    }
  }
  
  return allFiles;
} 