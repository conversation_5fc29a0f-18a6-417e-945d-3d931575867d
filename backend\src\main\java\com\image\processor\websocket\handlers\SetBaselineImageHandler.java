package com.image.processor.websocket.handlers;

import java.util.Map;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.ImageProcessorService;
import com.image.processor.websocket.TaskInfo;

/**
 * 设置基准图请求处理器
 * 注意: 已简化设计，移除sessionId，仅使用taskId作为基准图标识
 */
public class SetBaselineImageHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public SetBaselineImageHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "save_baseline";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        // 提取参数
        final String taskId = request.getString("taskId");
        
        logger.info("收到设置基准图请求: taskId={}", taskId);
        
        // 获取图像处理服务实例
        ImageProcessorService processor = ImageProcessorService.getInstance();
        
        // 创建任务信息
        final String requestId = request.has("requestId") ? request.getString("requestId") : createTaskId();
        TaskInfo taskInfo = new TaskInfo(requestId, conn);
        activeTasks.put(requestId, taskInfo);
        
        // 发送任务接收确认
        sendTaskUpdate(conn, requestId, "accepted", 0, "设置基准图任务已接受");
        
        // 提交任务到线程池
        taskExecutor.submit(() -> {
            try {
                // 如果任务已被取消，直接返回
                if (taskInfo.isCancelled()) {
                    logger.info("任务已被取消，不执行: {}", requestId);
                    return;
                }
                
                // 任务开始
                sendTaskUpdate(conn, requestId, "started", 0, "开始设置基准图");
                
                // 保存为基准图
                String baselineImagePath = processor.saveAsBaseline(taskId);
                
                // 检查结果
                if (baselineImagePath != null) {
                    JSONObject resultData = new JSONObject();
                    resultData.put("baselineImagePath", baselineImagePath);
                    resultData.put("baselineTaskId", taskId);
                    resultData.put("success", true);
                    
                    sendTaskUpdate(conn, requestId, "completed", 100, resultData);
                    logger.info("设置基准图成功: {}", baselineImagePath);
                } else {
                    sendTaskUpdate(conn, requestId, "failed", 0, "设置基准图失败：无法保存基准图");
                    logger.error("设置基准图失败: taskId={}", taskId);
                }
            } catch (Exception e) {
                logger.error("执行设置基准图任务时出错: " + requestId, e);
                sendTaskUpdate(conn, requestId, "error", 0, "设置基准图出错: " + e.getMessage());
            } finally {
                // 移除活动任务
                activeTasks.remove(requestId);
            }
        });
    }
} 