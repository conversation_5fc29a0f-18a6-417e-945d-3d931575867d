package com.image.processor.utils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基准图管理器
 * 负责管理基准图
 * 注意: 已简化设计，移除sessionId，仅使用taskId作为基准图标识
 */
public class BaselineImageManager {
    private static final Logger logger = LoggerFactory.getLogger(BaselineImageManager.class);
    
    // 单例实例
    private static BaselineImageManager instance;
    
    // 基准图任务ID到基准图路径的映射
    private final Map<String, String> baselineTaskToPathMap = new ConcurrentHashMap<>();
    
    // 限制缓存数量
    private static final int MAX_BASELINE_CACHE = 10;
    
    private BaselineImageManager() {
        logger.info("基准图管理器已初始化");
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized BaselineImageManager getInstance() {
        if (instance == null) {
            instance = new BaselineImageManager();
        }
        return instance;
    }
    
    /**
     * 设置基准图
     * 
     * @param baselineTaskId 基准图任务ID
     * @param baselineImagePath 基准图路径
     */
    public void setBaselineImage(String baselineTaskId, String baselineImagePath) {
        // 检查缓存大小
        if (baselineTaskToPathMap.size() >= MAX_BASELINE_CACHE) {
            // 移除最早的条目（简单实现）
            String oldestTaskId = baselineTaskToPathMap.keySet().iterator().next();
            baselineTaskToPathMap.remove(oldestTaskId);
            logger.debug("缓存已满，移除基准图任务: {}", oldestTaskId);
        }
        
        baselineTaskToPathMap.put(baselineTaskId, baselineImagePath);
        logger.info("已设置基准图: TaskID={}, 路径={}", baselineTaskId, baselineImagePath);
    }
    
    /**
     * 获取基准图路径
     * 
     * @param baselineTaskId 基准图任务ID
     * @return 基准图路径，不存在返回null
     */
    public String getBaselineImage(String baselineTaskId) {
        return baselineTaskToPathMap.get(baselineTaskId);
    }
    
    /**
     * 根据基准图路径获取任务ID
     * 
     * @param baselineImagePath 基准图路径
     * @return 对应的任务ID，不存在返回null
     */
    public String getBaselineTaskId(String baselineImagePath) {
        if (baselineImagePath == null) {
            return null;
        }
        
        // 遍历映射查找匹配的路径
        for (Map.Entry<String, String> entry : baselineTaskToPathMap.entrySet()) {
            if (baselineImagePath.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        
        return null;
    }
    
    /**
     * 移除基准图
     * 
     * @param baselineTaskId 基准图任务ID
     */
    public void removeBaselineImage(String baselineTaskId) {
        String baseline = baselineTaskToPathMap.remove(baselineTaskId);
        if (baseline != null) {
            logger.debug("已移除基准图: taskId={}", baselineTaskId);
        }
    }
    
    /**
     * 清理所有基准图缓存
     */
    public void clearAll() {
        baselineTaskToPathMap.clear();
        logger.info("已清理所有基准图缓存");
    }
    
    /**
     * 获取所有基准图路径
     * 
     * @return 所有基准图路径的集合
     */
    public Set<String> getAllBaselineImages() {
        return new HashSet<>(baselineTaskToPathMap.values());
    }
} 