<template>
  <div class="image-workspace-container">
    <!-- 图像显示区域 -->
    <div
        ref="imageContainerRef"
        class="image-display-area"
        @wheel.prevent="handleWheel"
        @mousedown="startPan"
        @mousemove="handlePan"
        @mouseup="endPan"
        @mouseleave="endPan"
        :class="{ 'panning': isPanning }"
    >
      <div class="empty-state" v-if="!displayUrl">
        <div>
          <el-icon style="font-size: 48px; margin-bottom: 10px;">
            <picture-rounded/>
          </el-icon>
          <div>请从左侧选择一张图片</div>
        </div>
      </div>
      <div v-else class="image-container" :style="imageContainerStyle">
        <div v-if="thumbnailPath && operationStack.length > 0" class="history-badge">已编辑 ({{
            operationStack.length
          }})
        </div>
        <img
            :src="displayUrl"
            :alt="imageFileName"
            @load="imageLoaded"
            :style="imageStyle"
            class="main-image"
            draggable="false"
            ref="imageRef"
        />
        <crop-overlay
            v-if="currentTool === 'crop' && imageWidth > 0 && imageHeight > 0"
            :active="currentTool === 'crop'"
            :width="imageWidth"
            :height="imageHeight"
            :transform="imageStyle.transform"
            :initial-crop="localEditParams.crop"
            :crop-ratio="localEditParams.cropRatio || 'free'"
            @update:crop="handleCropComplete"
            @crop-complete="handleCropComplete"
        />
      </div>
    </div>

    <!-- 编辑控制栏 -->
    <div class="edit-control-bar" v-if="selectedFile">
      <div class="zoom-controls">
        <el-button type="primary" size="small" :icon="ZoomOut" circle @click="zoom(0.9)"/>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
        <el-button type="primary" size="small" :icon="ZoomIn" circle @click="zoom(1.1)"/>
        <el-button type="primary" size="small" @click="resetZoom">适合屏幕</el-button>
      </div>

      <div class="transform-controls">
        <el-button
            type="primary"
            size="small"
            :icon="Back"
            circle
            @click="undo"
            :disabled="!canUndo"
            title="撤销"
        />
        <el-button
            type="primary"
            size="small"
            :icon="Right"
            circle
            @click="redo"
            :disabled="!canRedo"
            title="重做"
        />
      </div>

      <!-- 根据当前工具显示不同的控制 -->
      <div class="tool-specific-controls">
        <template v-if="currentTool === 'crop'">
          <el-button type="success" size="small" @click="applyCrop">应用裁剪</el-button>
          <el-button size="small" @click="cancelCrop">取消</el-button>
        </template>

        <template v-else-if="currentTool === 'adjust'">
          {/* Placeholder for adjustment controls */}
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, computed, watch, onMounted, onUnmounted} from 'vue';
import {ZoomIn, ZoomOut, Back, Right, PictureRounded} from '@element-plus/icons-vue';
import {ElMessage} from 'element-plus';
import historyService from '../services/history-service';
import baselineService from '../services/baseline-service';
import CropOverlay from './ToolPanel/CropOverlay.vue';

const props = defineProps({
  selectedFile: {
    type: String,
    default: ''
  },
  dispalyFile: {
    type: String,
    default: ''
  },
  editParams: {
    type: Object,
    default: () => ({})
  },
  currentBaselineImage: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update-params', 'operation-applied']);

// 图像容器引用
const imageContainerRef = ref(null);
const imageRef = ref(null);

// 图像状态
const displayUrl = ref('');
const thumbnailPath = ref(null);
const originalImagePath = ref('');
const currentTaskId = ref(null);
const imageWidth = ref(0);
const imageHeight = ref(0);
const imageLoaded = (e) => {
  const img = e.target;
  imageWidth.value = img.naturalWidth;
  imageHeight.value = img.naturalHeight;

  // 加载后自动适应屏幕
  resetZoom();

  // 确保图片居中显示
  centerImage();
};

// 编辑状态
const zoomLevel = ref(1);
const position = reactive({x: 0, y: 0});
const currentTool = ref(''); // Current active tool ('crop', 'adjust', etc.)

// Use a computed property for local edit params to react to prop changes
const localEditParams = computed(() => props.editParams);

// 平移状态
const isPanning = ref(false);
const lastPanPosition = reactive({x: 0, y: 0});

// 计算属性
const imageContainerStyle = computed(() => {
  return {
    transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px) rotate(${localEditParams.value.rotation || 0}deg)`,
  };
});

const imageStyle = computed(() => {
  return {
    transform: `scale(${zoomLevel.value})`,
    filter: baselineService.getFilterString(localEditParams.value),
  };
});

const imageFileName = computed(() => {
  if (!props.selectedFile) return '';
  // 提取文件名
  const parts = props.selectedFile.split(/[\/\\]/);
  return parts[parts.length - 1];
});

// 操作栈状态
const operationStack = computed(() => historyService.operationStack);
const redoStack = computed(() => historyService.redoStack);
const canUndo = computed(() => historyService.canUndo());
const canRedo = computed(() => historyService.canRedo());

// 方法
const zoom = (factor) => {
  zoomLevel.value = Math.max(0.1, Math.min(zoomLevel.value * factor, 5));
  centerImage();
};

const resetZoom = () => {
  if (!imageContainerRef.value || !imageWidth.value || !imageHeight.value) return;

  const container = imageContainerRef.value;
  const containerWidth = container.clientWidth;
  const containerHeight = container.clientHeight;

  // 计算合适的缩放比例，使图像适合容器
  const widthRatio = containerWidth / imageWidth.value;
  const heightRatio = containerHeight / imageHeight.value;

  // 使用最小比例确保图像完全可见
  zoomLevel.value = Math.min(widthRatio, heightRatio) * 0.95; // 留更多边距

  // 重置位置
  centerImage();
};

// 处理鼠标滚轮缩放
const handleWheel = (e) => {
  const zoomFactor = e.deltaY < 0 ? 1.1 : 0.9;
  zoom(zoomFactor);
};

// 平移相关方法
const startPan = (e) => {
  // Only pan if not using a tool like crop
  if (currentTool.value !== 'crop') {
    isPanning.value = true;
    imageContainerRef.value.style.cursor = 'grabbing';
    lastPanPosition.x = e.clientX;
    lastPanPosition.y = e.clientY;
  }
};

const handlePan = (e) => {
  if (!isPanning.value) return;

  const dx = e.clientX - lastPanPosition.x;
  const dy = e.clientY - lastPanPosition.y;

  position.x += dx;
  position.y += dy;

  lastPanPosition.x = e.clientX;
  lastPanPosition.y = e.clientY;
};

const endPan = () => {
  if (isPanning.value) {
    isPanning.value = false;
    imageContainerRef.value.style.cursor = 'grab';
  }
};

// 裁剪相关
const applyCrop = async () => {


  if (!localEditParams.value.crop) {
    ElMessage.warning('请先选择裁剪区域');
    return;
  }

  // 记录裁剪前的完整参数（深拷贝）
  const prevParams = JSON.parse(JSON.stringify(localEditParams.value));
  const prevCrop = prevParams.crop ? { ...prevParams.crop } : null; // 记录裁剪前的归一化裁剪参数
  const prevBaselineImagePath = props.currentBaselineImage;
  console.log(prevBaselineImagePath)
  console.log(prevBaselineImagePath)
  console.log(prevBaselineImagePath)
  // 将归一化坐标转换为像素坐标（直接乘以原始图片尺寸）
  const pixelCrop = {
    x: Math.round(localEditParams.value.crop.x * imageWidth.value),
    y: Math.round(localEditParams.value.crop.y * imageHeight.value),
    width: Math.round(localEditParams.value.crop.width * imageWidth.value),
    height: Math.round(localEditParams.value.crop.height * imageHeight.value)
  };

  console.log(localEditParams.value.crop)
  console.log(localEditParams.value.crop)

  try {
    const result = await window.electron.invoke('apply-image-edit', {
      filePath: props.selectedFile,
      baselineImagePath: prevBaselineImagePath, // 用 prop
      operation: 'crop',
      params: {crop: pixelCrop}
    });

    if (result.success) {
      const imageData = await window.electron.invoke('get-cached-image', {
        taskId: result.resultData.taskId,
        quality: 90
      });
      // 新的基准图路径
      const newBaselineImagePath = result.resultData.resultPath || result.resultData.baselinePath;
      // 生成裁剪后的完整参数
      const newParams = { ...prevParams, crop: { ...localEditParams.value.crop } };
      const newCrop = { ...localEditParams.value.crop };
      await historyService.addOperation({
        type: 'crop',
        params: newParams,
        filePath: props.selectedFile,
        baselineImagePath: newBaselineImagePath,
        prevBaselineImagePath,
        isOriginalImage: false,
        isInitialState: false,
        prevParams,
        prevCrop,
        newCrop,
        undo: async () => {
          // 撤销时用 prevCrop，需转为像素坐标
          let pixelCrop = null;
          if (prevCrop) {
            pixelCrop = {
              x: Math.round(prevCrop.x * imageWidth.value),
              y: Math.round(prevCrop.y * imageHeight.value),
              width: Math.round(prevCrop.width * imageWidth.value),
              height: Math.round(prevCrop.height * imageHeight.value)
            };
          }
          const undoParams = { ...prevParams, crop: prevCrop };
          emit('update-params', undoParams);
          await refreshImageWithBaseline('crop', { ...undoParams, crop: pixelCrop }, prevBaselineImagePath, result.resultData.taskId);
        },
        redo: async () => {
          // 重做时用 newCrop，需转为像素坐标
          let pixelCrop = null;
          if (newCrop) {
            pixelCrop = {
              x: Math.round(newCrop.x * imageWidth.value),
              y: Math.round(newCrop.y * imageHeight.value),
              width: Math.round(newCrop.width * imageWidth.value),
              height: Math.round(newCrop.height * imageHeight.value)
            };
          }
          const redoParams = { ...newParams, crop: newCrop };
          emit('update-params', redoParams);
          await refreshImageWithBaseline('crop', { ...redoParams, crop: pixelCrop }, newBaselineImagePath, result.resultData.taskId);
        }
      });
      ElMessage.success('裁剪已应用');
      currentTool.value = '';
      emit('update-params', { ...newParams });
      emit('update-image-url-display-base64', `${imageData.imageData}`);
    } else {
      ElMessage.error(result.error || '裁剪操作失败');
    }
  } catch (error) {
    console.error('裁剪错误:', error);
    ElMessage.error('裁剪过程中发生错误');
    currentTool.value = '';
  }
};

// 基于基准图和参数刷新图片内容
async function refreshImageWithBaseline(type, params, baselineImagePath,taskId) {
  console.log("11111111111")
  console.log(type)
  console.log(type)
  const imageState = baselineService.getImageState();
  const result = await window.electron.invoke('get-cached-image-apply-params', {
    taskId: taskId,
    type: type,
    filePath: props.selectedFile,
    params: JSON.parse(JSON.stringify(params)),
    baselineImagePath: props.currentBaselineImage,
    quality: 90,
    thumbnailPath : imageState.thumbnailPath
  });
  console.log("11111111111")
  console.log("11111111111")
  if (result && result.imageData) {
    emit('update-image-url-display-base64', result.imageData);
  }
}

const cancelCrop = () => {
  console.log("ImageWorkspace: Cancelling crop");
  currentTool.value = ''; // Deactivate tool
  // Optionally revert any temporary crop parameter changes if needed
  // emit('update-params', { crop: originalCropValue }); // Restore if needed
};

// 由 CropOverlay 触发，更新当前的裁剪区域 (归一化坐标)
const updateCropRect = (normalizedCropRect) => {
  // console.log("ImageWorkspace: Update crop rect (normalized):", normalizedCropRect);
  // Don't directly emit here, wait for crop-complete or applyCrop
  // We just keep the latest crop value internally for applyCrop
  // Use Object.assign or spread syntax if editParams were reactive locally
  // But since it's computed from props, we rely on parent update
  // This function might only be needed for real-time display if any
};

// 由 CropOverlay 触发，表示用户完成了拖拽/调整
const handleCropComplete = (finalNormalizedCropRect) => {
  console.log("ImageWorkspace: Crop interaction complete (normalized):", finalNormalizedCropRect);
  // Update the parent's state which will flow back down via props
  emit('update-params', {crop: finalNormalizedCropRect});
};

// 更新参数 (通用，由基础调整等触发)
const updateParams = (updatedValues) => {
  console.log("ImageWorkspace: updating params", updatedValues);
  emit('update-params', updatedValues);
};

// 撤销操作
const undo = async () => {
  const operation = await historyService.undo();
  console.log("撤销操作", operation);
  if (operation) {
    // 发送 operation-applied 事件来更新图片
    emit('operation-applied', {
      operationStack: historyService.operationStack.map(op => {
        const {undo, redo, ...rest} = op;
        return rest;
      }),
      currentFile: props.selectedFile
    });
  }
};

// 重做操作
const redo = async () => {
  const operation = await historyService.redo();
  console.log("重做操作", operation);
  if (operation) {
    // 发送 operation-applied 事件来更新图片
    emit('operation-applied', {
      operationStack: historyService.operationStack.map(op => {
        const {undo, redo, ...rest} = op;
        return rest;
      }),
      currentFile: props.selectedFile
    });
  }
};

// 加载图片
const loadImage = async (filePath) => { // Removed isReload param for simplicity now
  console.log(`ImageWorkspace: Loading image ${filePath}`);
  position.x = 0;
  position.y = 0;
  currentTool.value = ''; // Reset tool on new image load
  imageWidth.value = 0; // Reset dimensions before loading
  imageHeight.value = 0;
  displayUrl.value = ''; // Clear previous image immediately

  try {
    await baselineService.loadImage(
        filePath,
        null, // No longer passing isReload directly
        historyService.currentModuleId, // Need to manage moduleId correctly
        (newUrl) => { // onImageLoaded
          displayUrl.value = newUrl;
          const imageState = baselineService.getImageState();
          originalImagePath.value = imageState.originalImagePath;
          thumbnailPath.value = imageState.thumbnailPath;
          currentTaskId.value = imageState.currentTaskId;
          // Wait for image element to load its dimensions
        },
        () => { // onParamsReset
          console.log("ImageWorkspace: Received params reset callback");
          // Reset all editable params managed by ToolPanel via App.vue
          emit('update-params', {
            brightness: 0, contrast: 0, saturation: 0, sharpness: 0,
            rotation: 0, crop: null, cropRatio: 'free',
            flipX: false, flipY: false
            /* add other resettable params */
          });
        },
        (params) => { // onParamsUpdate (from history or baseline)
          console.log("ImageWorkspace: Received params update callback", params);
          emit('update-params', params);
        }
    );

    // Load operation history AFTER baseline image/params are potentially set
    await historyService.loadOperationHistory(
        filePath,
        () => { /* History reset callback - likely same as above */
          emit('update-params', {
            brightness: 0,
            contrast: 0,
            saturation: 0,
            sharpness: 0,
            rotation: 0,
            crop: null,
            cropRatio: 'free',
            flipX: false,
            flipY: false
          });
        },
        (params) => { /* History update callback - likely same as above */
          emit('update-params', params);
        }
    );

  } catch (error) {
    console.error("Error in loadImage:", error);
    ElMessage.error("加载图像或历史记录时出错");
    displayUrl.value = ''; // Clear display on error
  }
};

// 添加代码确保图片居中
const centerImage = () => {
  if (!imageContainerRef.value) return;

  // 重置位置到中心
  position.x = 0;
  position.y = 0;
};

// 监听选中的文件变化
watch(() => props.selectedFile, (newFile, oldFile) => {
  if (newFile && newFile !== oldFile) {
    console.log(newFile)
    console.log(oldFile)
    console.log("````````````````````````````````")
    console.log("````````````````````````````````")
    console.log("````````````````````````````````")
    loadImage(newFile);
  } else if (!newFile) {
    // Clear state when no file is selected
    displayUrl.value = '';
    thumbnailPath.value = null;
    originalImagePath.value = null;
    currentTaskId.value = null;
    // historyService.reset(); // Reset history service
    emit('update-params', {rotation: 0, crop: null, cropRatio: 'free', flipX: false, flipY: false /* other params */});
    currentTool.value = '';
  }
});

// 监听显示图像地址变化
watch(() => props.dispalyFile, (newFile, oldFile) => {
  if (newFile && newFile !== oldFile) {
    displayUrl.value = newFile;
  } else if (!newFile) {
    // Clear state when no file is selected
    displayUrl.value = '';
    thumbnailPath.value = null;
    originalImagePath.value = null;
    currentTaskId.value = null;
    // historyService.reset(); // Reset history service
    emit('update-params', {rotation: 0, crop: null, cropRatio: 'free', flipX: false, flipY: false /* other params */});
    currentTool.value = '';
  }
});


// 监听来自父组件的 editParams 变化 (不需要直接 watch localEditParams)
watch(() => props.editParams, async (newParams) => {
  // 参数变化时自动刷新图片内容
 /* if (props.selectedFile && newParams && newParams.crop) {
    console.log("0000000000000000")
    console.log("0000000000000000")
    console.log("0000000000000000")
    try {
      // 这里假设你有 taskId 或用参数生成唯一key，如果没有 taskId 就用参数
      const result = await window.electron.invoke('get-cached-image', {
        filePath: props.selectedFile,
        params: JSON.parse(JSON.stringify(newParams)), // 传递普通对象，避免 Proxy
        quality: 90
      });
      if (result && result.imageData) {
        displayUrl.value = result.imageData; // base64 或 url
      }
    } catch (e) {
      console.error('刷新图片内容失败', e);
    }
  }*/
}, {deep: true});

// **重要**: 添加对 currentTool 的监听或通过方法设置
// Public method to be called by App.vue when tool changes
const setCurrentTool = (tool) => {
  console.log("ImageWorkspace: Setting current tool to", tool);
  currentTool.value = tool;
  if (tool === 'crop' && !localEditParams.value.crop) {
    // If activating crop without existing crop data, initialize it
    // CropOverlay will handle the initial rect calculation via its initCropArea
    console.log("Activating crop tool, overlay will initialize.");
  }
};
// Expose method for parent component (App.vue)
defineExpose({setCurrentTool});

// 设置预览更新监听
onMounted(() => {
  // 如果有选中文件，加载图片
  if (props.selectedFile) {
    loadImage(props.selectedFile);
  }

  // 设置预览更新监听器
  baselineService.setupPreviewUpdateListener((newUrl) => {
    // 更新显示URL
    displayUrl.value = newUrl;

    // 获取最新状态
    const imageState = baselineService.getImageState();
    originalImagePath.value = imageState.originalImagePath;
    thumbnailPath.value = imageState.thumbnailPath;
    currentTaskId.value = imageState.currentTaskId;
  });

  window.addEventListener('resize', updateOverlayPosition);
});

// 组件卸载前清理
onUnmounted(() => {
  endPan();
  // 移除预览更新监听器
  baselineService.removePreviewUpdateListener();
  window.removeEventListener('resize', updateOverlayPosition);
});

const overlayPosition = ref({left: 0, top: 0});

const updateOverlayPosition = () => {
  if (imageRef.value && imageContainerRef.value) {
    const imgRect = imageRef.value.getBoundingClientRect();
    const containerRect = imageContainerRef.value.getBoundingClientRect();
    overlayPosition.value.left = imgRect.left - containerRect.left;
    overlayPosition.value.top = imgRect.top - containerRect.top;
  }
};

watch([displayUrl, zoomLevel, position], updateOverlayPosition);
</script>

<style scoped>
.image-workspace-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 继承父组件背景色和文字颜色 */
}

.image-display-area {
  flex: 1;
  position: relative; /* 确保相对定位，为子元素绝对定位提供参照 */
  overflow: hidden;
  background-color: var(--bg-color); /* 使用变量 */
  background-image: linear-gradient(45deg, var(--bg-color-light) 25%, transparent 25%),
  linear-gradient(-45deg, var(--bg-color-light) 25%, transparent 25%),
  linear-gradient(45deg, transparent 75%, var(--bg-color-light) 75%),
  linear-gradient(-45deg, transparent 75%, var(--bg-color-light) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  cursor: grab; /* Default cursor for panning */
}

.image-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center center; /* Ensure rotation happens around the center */
  transition: transform 0.1s ease;
}

.main-image {
  display: block;
  transition: transform 0.2s ease, filter 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); /* 暗色主题下阴影可以更明显 */
  user-select: none; /* Prevent image dragging */
  -webkit-user-drag: none; /* Prevent image dragging in WebKit */
}

.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color-secondary); /* 使用变量 */
  text-align: center;
}

.edit-control-bar {
  height: 60px;
  border-top: 1px solid var(--border-color); /* 使用变量 */
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-color-light); /* 使用变量 */
}

.zoom-controls, .transform-controls, .tool-specific-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  font-size: 14px;
  width: 50px;
  text-align: center;
  color: var(--text-color); /* 使用变量 */
}

.panning {
  cursor: grabbing;
}

/* 新增：原图标签样式 */
.original-image-badge {
  position: absolute;
  top: 10px;
  left: 10px; /* Or right: 10px */
  background-color: rgba(0, 128, 0, 0.8); /* Green background */
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
  pointer-events: none;
}

.history-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 165, 0, 0.8); /* Orange background */
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
  pointer-events: none; /* Avoid interfering with mouse events */
}
</style> 