package com.image.processor.websocket.handlers;

import java.util.Map;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.ImageProcessorService;
import com.image.processor.core.ProgressCallback;
import com.image.processor.websocket.TaskInfo;

/**
 * 旋转图像请求处理器
 * 注意：当前是空实现，等待ImageProcessorService实现rotateImage方法
 */
public class RotateImageHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public RotateImageHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "rotate_image";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        // 提取参数
        final String taskId = request.getString("taskId");
        final String inputPath = request.getString("inputPath");
        final JSONObject cropJson = request.getJSONObject("params");
        final int angle = cropJson.getInt("angle");
        final String sessionId = request.optString("sessionId", getClientId(conn));
        
        // 输出路径可选
        final String outputPath = request.has("outputPath") ? request.getString("outputPath") : null;
        
        logger.info("收到旋转图像请求: taskId={}, 输入={}, 角度={}", taskId, inputPath, angle);
        
        // 发送任务接收确认
        sendTaskUpdate(conn, taskId, "accepted", 0, "任务已接受，准备处理");
        
        // 创建并存储任务信息
        TaskInfo taskInfo = new TaskInfo(taskId, conn);
        activeTasks.put(taskId, taskInfo);
        
        // 提交任务到线程池
        taskExecutor.submit(() -> {
            try {
                // 如果任务已被取消，直接返回
                if (taskInfo.isCancelled()) {
                    logger.info("任务已被取消，不执行: {}", taskId);
                    return;
                }
                
                // 任务开始
                sendTaskUpdate(conn, taskId, "started", 0, "开始旋转图像");
                
                // 获取图像处理服务实例
                ImageProcessorService processor = ImageProcessorService.getInstance();
                
                // 创建进度回调
                final ProgressCallback progressCallback = new ProgressCallback() {
                    @Override
                    public boolean onProgress(int progress, String message) {
                        if (taskInfo.isCancelled()) {
                            return false; // 返回false表示任务已取消，应停止处理
                        }
                        sendTaskUpdate(conn, taskId, "progress", progress, message);
                        return true; // 返回true表示继续处理
                    }
                };


                ImageProcessorService.ProcessingResult result = processor.applyRotateImage(inputPath, angle, progressCallback,taskId);

//                // 临时空实现，等待ImageProcessorService实现rotateImage方法
//                logger.warn("旋转图像功能尚未实现，返回临时成功状态");
//                sendTaskUpdate(conn, taskId, "progress", 50, "正在处理图像旋转...");
//
//                // 模拟处理延迟
//                try {
//                    Thread.sleep(500);
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                }
//
                // 任务已被取消
                if (taskInfo.isCancelled()) {
                    logger.info("任务在处理过程中被取消: {}", taskId);
                    sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
                    return;
                }


                if (result != null) {
                    String resultPath = null;
                    if (outputPath != null) {
                        resultPath = processor.saveProcessedImage(result, outputPath);
                    }
                    JSONObject resultData = new JSONObject();
                    resultData.put("taskId", taskId);
                    resultData.put("resultPath", resultPath);
                    sendTaskUpdate(conn, taskId, "completed", 100, resultData);
                    logger.info("旋转请求已处理: taskId={}", taskId);
                    result.release();
                } else {
                    sendTaskUpdate(conn, taskId, "error", 0, "旋转失败");
                }
//
//
//                // 发送成功结果（临时实现）
//                JSONObject resultData = new JSONObject();
//                resultData.put("taskId", taskId);
//                resultData.put("message", "旋转功能尚未实现");
//
//                sendTaskUpdate(conn, taskId, "completed", 100, resultData);
//                logger.info("旋转图像请求已处理（临时实现）: taskId={}", taskId);
                
            } catch (Exception e) {
                logger.error("执行旋转任务时出错: taskId=" + taskId, e);
                sendTaskUpdate(conn, taskId, "error", 0, "处理出错: " + e.getMessage());
            } finally {
                // 移除活动任务
                activeTasks.remove(taskId);
            }
        });
    }
} 