<template>
  <div class="basic-adjustment">
    <div class="adjustment-group">
      <div v-for="param in paramsList" :key="param.name" class="adjustment-item">
        <div class="slider-header">
          <span>{{ param.label }}</span>
          <span class="value">{{ params[param.name] }}</span>
        </div>
        <el-slider
          v-model="params[param.name]"
          :min="param.min"
          :max="param.max"
          :step="param.step"
          @change="updateParams"
          show-stops
        ></el-slider>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import {ElMessage} from "element-plus";
import TaskIdUtils from "@/utils/TaskIdUtils";
import baselineService from "@/services/baseline-service";
import historyService from "@/services/history-service";

const props = defineProps({
  selectedFile: {
    type: String,
    default: ''
  },
  currentBaselineImage: {
    type: String,
    default: ''
  },
  displayUrl: {
    type: String,
    default: ''
  },
  params: {
    type: Object,
    required: true
  },
  id: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update-params','update-last-task-id']);
let lastParams = JSON.parse(JSON.stringify(props.params))

// 参数列表定义
const paramsList = [
  { name: 'brightness', label: '亮度', min: -100, max: 100, step: 1 },
  { name: 'contrast', label: '对比度', min: -100, max: 100, step: 1 },
  { name: 'saturation', label: '饱和度', min: -100, max: 100, step: 1 },
  { name: 'sharpness', label: '锐度', min: 0, max: 100, step: 1 },
];

// 初始化默认状态
const initDefaultState = async () => {
  const hasno = await historyService._hasNoOperations();
  if (hasno) {
    console.log("111111111111111111111111111111111111")
    console.log("historyService")
/*    await historyService.addOperation({
      type: 'adjustParams',
      params: {
        brightness: 0,
        contrast: 0,
        saturation: 0,
        sharpness: 0,
        denoise: 0,
        rotation: 0,
        crop: null
      },
      moduleId: props.id,
      filePath: props.selectedFile,
      isOriginalImage: false
    });*/
  }
};

// 组件挂载时初始化
onMounted(() => {
  if (props.selectedFile) {
    initDefaultState();
  }
});

// 监听文件变化
watch(() => props.selectedFile, (newFile) => {
  if (newFile) {
    initDefaultState();
  }
});

// 更新参数
const updateParams = async () => {
  // 变更前参数快照
  const prevParams = JSON.parse(JSON.stringify(lastParams));

  console.log(`BasicAdjustment: 更新参数, 组件ID: ${props.id}`, props.params);
  const imageState = baselineService.getImageState();
  // 深拷贝参数以确保可序列化
  const paramsCopy = JSON.parse(JSON.stringify(props.params));
  console.log(prevParams)
  console.log(paramsCopy)

  const taskId = await TaskIdUtils.createTaskId(props.selectedFile);

  const processResult = await window.electron.invoke('basic_adjust_params', {
    inputPath: imageState.thumbnailPath,
    taskId: taskId,
    originalPath: props.selectedFile,
    currentBaselineImage: props.currentBaselineImage,
    params: paramsCopy
  });

  if (!processResult || !processResult.success) {
    const errorMsg = processResult?.error || '模块处理失败：未知错误';
    console.error('模块处理失败:', errorMsg);
    ElMessage.error(`模块处理失败: ${errorMsg}`);
    throw new Error(errorMsg);
  }
  // 3. 使用taskId获取图像数据
  if (processResult.success) {
    const imageData = await window.electron.invoke('get-cached-image', {
      taskId: taskId,
      quality: 90
    });

    console.log('模块处理成功:', processResult);
    console.log("paramsCopy", paramsCopy);

    const hasno = await historyService._hasNoOperations();
    if (hasno) {
      // 添加初始状态，并标记为初始状态
      await historyService.addOperation({
        type: 'adjustParams',
        params: {
          brightness: 0,
          contrast: 0,
          saturation: 0,
          sharpness: 0,
          denoise: 0,
          rotation: 0,
          crop: null
        },
        moduleId: props.id,
        filePath: props.selectedFile,
        isOriginalImage: false,
        isInitialState: true  // 添加初始状态标志
      });
    }
    // 变更后参数快照
    const newParams = JSON.parse(JSON.stringify(props.params));
    // 添加当前操作状态
    await historyService.addOperation({
      type: 'adjustParams',
      params: newParams,
      prevParams,
      newParams,
      moduleId: props.id,
      filePath: props.selectedFile,
      isOriginalImage: false,
      isInitialState: false,  // 标记为非初始状态
      undo: async () => {
        let pixelCrop = null;
        // if (prevParams.crop) {
        //   pixelCrop = {
        //     x: Math.round(prevParams.crop.x * imageWidth.value),
        //     y: Math.round(prevParams.crop.y * imageHeight.value),
        //     width: Math.round(prevParams.crop.width * imageWidth.value),
        //     height: Math.round(prevParams.crop.height * imageHeight.value)
        //   };
        // }
        const undoParams = { ...prevParams };
        emit('update-params', undoParams);
        await refreshImageWithBaseline('adjustParams', pixelCrop ? { ...undoParams, crop: pixelCrop } : undoParams,  props.currentBaselineImage, processResult.resultData.taskId);
      },
      redo: async () => {
        let pixelCrop = null;
        // if (newParams.crop) {
        //   pixelCrop = {
        //     x: Math.round(newParams.crop.x * imageWidth.value),
        //     y: Math.round(newParams.crop.y * imageHeight.value),
        //     width: Math.round(newParams.crop.width * imageWidth.value),
        //     height: Math.round(newParams.crop.height * imageHeight.value)
        //   };
        // }
        const redoParams = { ...newParams };
        emit('update-params', redoParams);
        await refreshImageWithBaseline('adjustParams', pixelCrop ? { ...redoParams, crop: pixelCrop } : redoParams,  props.currentBaselineImage, processResult.resultData.taskId);
      }
    });

    emit('update-image-url-display-base64',`${imageData.imageData}`);
    emit('update-last-task-id', taskId);  // 触发更新 lastTaskId 的事件

  }
  lastParams = JSON.parse(JSON.stringify(paramsCopy));
};

// 基于基准图和参数刷新图片内容
async function refreshImageWithBaseline(type, params, baselineImagePath,taskId) {
  const imageState = baselineService.getImageState();
  const result = await window.electron.invoke('get-cached-image-apply-params', {
    taskId: taskId,
    type: type,
    filePath: props.selectedFile,
    params: JSON.parse(JSON.stringify(params)),
    baselineImagePath: props.currentBaselineImage,
    quality: 90,
    thumbnailPath : imageState.thumbnailPath
  });
  if (result && result.imageData) {
    emit('update-image-url-display-base64', result.imageData);
  }
}

// 监听props.params变化
watch(() => props.params, (newParams) => {
  console.log('BasicAdjustment: 接收到新参数:', newParams, '组件ID:', props.id);
}, { deep: true, immediate: true });
</script>

<style scoped>
.basic-adjustment {
  padding: 0.5rem 0;
}

.adjustment-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.adjustment-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value {
  font-weight: bold;
  min-width: 30px;
  text-align: right;
}
</style> 