package com.image.processor.utils;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务ID工具类
 * 专门负责生成和获取taskId，确保前后端一致
 */
public class TaskIdUtils {
    private static final Logger logger = LoggerFactory.getLogger(TaskIdUtils.class);
    
    /**
     * 计算文件的哈希值
     * 使用与calculateStringHash相同的djb2算法
     * 
     * @param file 要计算哈希值的文件
     * @return 文件的哈希值，失败返回null
     */
    public static String calculateFileHash(File file) {
        if (file == null || !file.exists() || !file.isFile() || !file.canRead()) {
            logger.error("无效的文件对象或文件不可读: {}", file);
            return null;
        }
        
        try {
            // 使用文件路径计算哈希值，确保与前端一致
            String normalizedPath = normalizePath(file.getAbsolutePath());
            return calculateStringHash(normalizedPath);
        } catch (Exception e) {
            logger.error("计算文件哈希值时出错: {}", file.getAbsolutePath(), e);
            return null;
        }
    }
    
    /**
     * 计算文件的哈希值
     * 
     * @param filePath 文件路径
     * @return 文件的哈希值，失败返回null
     */
    public static String calculateFileHash(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            logger.error("无效的文件路径");
            return null;
        }
        
        // 使用路径字符串计算哈希值，确保与前端一致
        String normalizedPath = normalizePath(filePath);
        return calculateStringHash(normalizedPath);
    }
    
    /**
     * 根据输入字符串计算哈希值
     * 使用与前端JavaScript一致的djb2算法:
     * static calculateStringHash(input) {
     *   let hash = 5381;
     *   for (let i = 0; i < input.length; i++) {
     *     hash = ((hash << 5) + hash) + input.charCodeAt(i); // hash * 33 + c
     *   }
     *   return (hash >>> 0).toString(16).padStart(40, '0');
     * }
     * 
     * @param input 输入字符串
     * @return 哈希值，失败返回null
     */
    public static String calculateStringHash(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            // 使用与前端一致的djb2算法
            long hash = 5381;
            for (int i = 0; i < input.length(); i++) {
                hash = ((hash << 5) + hash) + input.charAt(i);  // hash * 33 + c
            }
            
            // 确保是无符号整数
            hash = hash & 0xFFFFFFFFL;  // 使用32位无符号长整数
            
            // 转为16进制并填充到40位
            String hexHash = Long.toHexString(hash);
            
            // 前导0填充到40位
            StringBuilder sb = new StringBuilder();
            for (int i = hexHash.length(); i < 40; i++) {
                sb.append('0');
            }
            sb.append(hexHash);
            
            return sb.toString();
        } catch (Exception e) {
            logger.error("计算字符串哈希值时出错", e);
            return null;
        }
    }
    
    /**
     * 生成标准任务ID
     * 根据输入路径生成一个一致的ID
     * @param imagePath 图像路径aa
     * @return 任务ID（SHA-1哈希值）
     */
    public static String createTaskId(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            logger.error("图像路径为空");
            return null;
        }
        
        // 标准化路径
        String normalizedPath = normalizePath(imagePath);
        
        // 直接使用图像路径计算哈希值
        return calculateStringHash(normalizedPath);
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    /**
     * 标准化文件路径
     * 注意：此方法必须与前端TaskIdUtils.js中的normalizePath方法保持一致
     * 
     * @param path 输入路径
     * @return 标准化的路径
     */
    public static String normalizePath(String path) {
        if (path == null) {
            return "";
        }
        
        // 1. 处理 file:// 协议前缀
        if (path.startsWith("file://")) {
            path = path.substring(7);
        }
        
        // 2. 替换所有反斜杠为正斜杠 (使用replace而不是replaceAll，确保行为一致)
        String normalizedPath = path.replace('\\', '/');
        
        // 3. 处理Windows驱动器格式 (/C:/xxx -> C:/xxx)
        if (normalizedPath.length() > 3 && 
            normalizedPath.charAt(0) == '/' && 
            Character.isLetter(normalizedPath.charAt(1)) && 
            normalizedPath.charAt(2) == ':' &&
            normalizedPath.charAt(3) == '/') {
            normalizedPath = normalizedPath.substring(1);
        }
        
        return normalizedPath;
    }

    /**
     * 标准化文件路径
     * 注意：此方法必须与前端TaskIdUtils.js中的normalizePath方法保持一致
     *
     * @param path 输入路径
     * @return 标准化的路径
     */
    public static String normalizePathAndSuffix(String path) {
        if (path == null) {
            return "";
        }

        // 1. 处理 file:// 协议前缀
        if (path.startsWith("file://")) {
            path = path.substring(7);
        }

        // 2. 替换所有反斜杠为正斜杠 (使用replace而不是replaceAll，确保行为一致)
        String normalizedPath = path.replace('\\', '/');

        // 3. 处理Windows驱动器格式 (/C:/xxx -> C:/xxx)
        if (normalizedPath.length() > 3 &&
                normalizedPath.charAt(0) == '/' &&
                Character.isLetter(normalizedPath.charAt(1)) &&
                normalizedPath.charAt(2) == ':' &&
                normalizedPath.charAt(3) == '/') {
            normalizedPath = normalizedPath.substring(1);
        }

        if(normalizedPath.contains("?t=")){
            normalizedPath = normalizedPath.substring(0,normalizedPath.indexOf("?t="));
        }

        return normalizedPath;
    }
} 