package com.image.processor.websocket.handlers;

import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.ImageProcessorService;

/**
 * 获取缓存图像处理器
 */
public class GetCachedImageHandler extends BaseRequestHandler {
    
    public GetCachedImageHandler(ExecutorService taskExecutor) {
        super(taskExecutor);
    }

    @Override
    public String getActionName() {
        return "get_cached_image";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        // 提取参数
        final String taskId = request.getString("taskId");
        final int quality = request.has("quality") ? request.getInt("quality") : 85;
        
        logger.info("收到获取缓存图像请求: taskId={}, quality={}", taskId, quality);
        
        // 获取图像处理服务实例
        ImageProcessorService processor = ImageProcessorService.getInstance();
        
        // 异步处理请求
        taskExecutor.submit(() -> {
            try {
                // 获取图像字节数据
                byte[] imageData = null;
                String format = "jpeg";
                
                try {
                    // 检查缓存中是否有指定taskId
                    if (processor.hasTaskIdInCache(taskId)) {
                        // 直接使用前端传入的taskId获取缓存图像
                        imageData = processor.getCachedImageBytes(taskId, quality);
                        logger.info("成功获取缓存图像: taskId={}, 图像大小: {} KB", taskId, imageData.length / 1024);
                    } else {
                        // 缓存中没有此taskId，通知前端需要重新处理
                        logger.warn("缓存中不存在taskId: {}", taskId);
                        sendImageError(conn, taskId, "缓存中不存在此任务ID，需要创建功能基准图", "NO_MODULE_BASELINE");
                        return;
                    }
                } catch (ImageProcessorService.NoBaselineImageException e) {
                    // 如果找不到基准图，通知前端需要创建基准图
                    logger.error("无法找到关联的基准图: {}", taskId);
                    sendImageError(conn, taskId, "无法找到关联的基准图: " + e.getMessage(), "NO_BASELINE_IMAGE");
                    return;
                } catch (Exception e) {
                    logger.error("获取缓存图像时出错: taskId={}", taskId, e);
                    sendImageError(conn, taskId, "获取缓存图像失败: " + e.getMessage());
                    return;
                }
                
                if (imageData == null) {
                    sendImageError(conn, taskId, "缓存图像为空");
                    return;
                }
                
                // 发送图像数据
                JSONObject message = new JSONObject();
                message.put("type", "image_data");
                message.put("taskId", taskId);
                message.put("format", format);
                
                // 不能直接将字节数组放入JSONObject，所以使用Base64
                String base64Image = "data:image/" + format + ";base64," + java.util.Base64.getEncoder().encodeToString(imageData);
                message.put("data", base64Image);
                
                conn.send(message.toString());
                logger.info("已发送图像数据: taskId={}, 大小: {} KB", taskId, imageData.length / 1024);
                
            } catch (Exception e) {
                logger.error("处理获取缓存图像请求时出错: {}", e.getMessage(), e);
                sendImageError(conn, taskId, "处理请求失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 发送图像错误消息
     */
    private void sendImageError(WebSocket conn, String taskId, String message) {
        sendImageError(conn, taskId, message, null);
    }
    
    /**
     * 发送图像错误消息，带错误代码
     */
    private void sendImageError(WebSocket conn, String taskId, String message, String errorCode) {
        try {
            JSONObject response = new JSONObject();
            response.put("type", "error");
            response.put("taskId", taskId);
            response.put("message", message);
            
            if (errorCode != null) {
                response.put("code", errorCode);
            }
            
            conn.send(response.toString());
            logger.error("已发送错误响应: taskId={}, 错误: {}, 代码: {}", taskId, message, errorCode);
        } catch (Exception e) {
            logger.error("发送错误响应时出错", e);
        }
    }
} 
 
 