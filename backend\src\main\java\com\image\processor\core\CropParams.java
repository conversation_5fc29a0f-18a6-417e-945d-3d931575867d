package com.image.processor.core;

/**
 * 图像裁剪参数类
 */
public class CropParams implements ModuleParams {
    private int x;        // 裁剪区域左上角x坐标
    private int y;        // 裁剪区域左上角y坐标
    private int width;    // 裁剪区域宽度
    private int height;   // 裁剪区域高度
    private double rotation; // 旋转角度（可选）
    
    /**
     * 默认构造函数
     */
    public CropParams() {
        this.x = 0;
        this.y = 0;
        this.width = 0;
        this.height = 0;
        this.rotation = 0.0;
    }
    
    /**
     * 构造函数
     * 
     * @param x 裁剪区域左上角x坐标
     * @param y 裁剪区域左上角y坐标
     * @param width 裁剪区域宽度
     * @param height 裁剪区域高度
     */
    public CropParams(int x, int y, int width, int height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.rotation = 0.0;
    }
    
    /**
     * 构造函数
     * 
     * @param x 裁剪区域左上角x坐标
     * @param y 裁剪区域左上角y坐标
     * @param width 裁剪区域宽度
     * @param height 裁剪区域高度
     * @param rotation 旋转角度
     */
    public CropParams(int x, int y, int width, int height, double rotation) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.rotation = rotation;
    }
    
    public int getX() {
        return x;
    }
    
    public void setX(int x) {
        this.x = x;
    }
    
    public int getY() {
        return y;
    }
    
    public void setY(int y) {
        this.y = y;
    }
    
    public int getWidth() {
        return width;
    }
    
    public void setWidth(int width) {
        this.width = width;
    }
    
    public int getHeight() {
        return height;
    }
    
    public void setHeight(int height) {
        this.height = height;
    }
    
    public double getRotation() {
        return rotation;
    }
    
    public void setRotation(double rotation) {
        this.rotation = rotation;
    }
    
    @Override
    public boolean isEmpty() {
        // 裁剪参数为空的条件：宽度或高度为0，或者等于原图尺寸且无旋转
        return width <= 0 || height <= 0 || (x == 0 && y == 0 && Math.abs(rotation) < 0.01);
    }
    
    @Override
    public String getParamType() {
        return "crop";
    }
    
    @Override
    public String toJsonString() {
        return String.format(
            "{\"type\":\"%s\",\"x\":%d,\"y\":%d,\"width\":%d,\"height\":%d,\"rotation\":%.2f}",
            getParamType(), x, y, width, height, rotation
        );
    }
    
    @Override
    public boolean fromJsonString(String jsonString) {
        try {
            // 简单解析JSON，实际应用中可以使用更完善的JSON库
            if (jsonString == null || !jsonString.contains(getParamType())) {
                return false;
            }
            
            // 这里简化处理，实际应用中需要更完善的解析逻辑
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String toString() {
        return String.format(
            "CropParams[x=%d, y=%d, width=%d, height=%d, rotation=%.2f]",
            x, y, width, height, rotation
        );
    }
} 