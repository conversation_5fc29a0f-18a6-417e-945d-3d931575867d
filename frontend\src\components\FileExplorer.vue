<template>
  <div class="file-explorer-container">
    <div class="explorer-header">
      <h3>文件浏览器</h3>
      <div class="header-actions">
        <el-tooltip content="打开文件夹" placement="top">
          <el-button type="primary" size="small" :icon="Folder" circle @click="openDirectory" />
        </el-tooltip>
        <el-tooltip content="刷新目录" placement="top">
          <el-button type="primary" size="small" :icon="Refresh" circle @click="refreshDirectory" :disabled="!directory" />
        </el-tooltip>
      </div>
    </div>

    <div v-if="!directory" class="empty-state">
      <el-empty description="请选择文件夹以开始浏览">
        <el-button type="primary" @click="openDirectory">
          <el-icon><Folder /></el-icon>
          打开文件夹
        </el-button>
      </el-empty>
    </div>

    <div v-else class="file-tree-container">
      <el-tree
        ref="fileTreeRef"
        :data="fileTreeData"
        :props="defaultProps"
        node-key="path"
        :load="loadNode"
        lazy
        :default-expanded-keys="expandedKeys"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <span class="node-icon">
              <el-icon v-if="isFolder(data)"><Folder /></el-icon>
              <el-icon v-else-if="isImage(data)"><Picture /></el-icon>
              <el-icon v-else><Document /></el-icon>
            </span>
            <el-tooltip :content="data.label" placement="top" :disabled="!isLongFilename(data.label)">
              <div class="file-name-container">
                <span class="file-name">{{ getTruncatedName(data.label) }}</span>
              </div>
            </el-tooltip>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage, ElTree } from 'element-plus';
import { Folder, Document, Picture, Refresh } from '@element-plus/icons-vue';
import * as path from 'path';

const props = defineProps({
  directory: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['select-file', 'open-directory']);

const fileTreeRef = ref(null);
const fileTreeData = ref([]);
const expandedKeys = ref([]);
const selectedPath = ref('');
const loading = ref(false);

// 文件树属性配置
const defaultProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'isLeaf'
};

// 判断是否为文件夹
const isFolder = (data) => {
  return data.type === 'directory';
};

// 判断是否为图像文件
const isImage = (data) => {
  if (!data.path) return false;
  const ext = data.path.toLowerCase().split('.').pop();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'].includes(ext);
};

// 判断文件名是否过长需要截断
const isLongFilename = (filename) => {
  return filename.length > 18;
};

// 获取截断的文件名
const getTruncatedName = (filename) => {
  if (!isLongFilename(filename)) return filename;
  
  const parts = filename.split('.');
  const ext = parts.pop();
  const name = parts.join('.');
  
  if (name.length <= 10) return filename;
  
  return `${name.substring(0, 8)}...${ext}`;
};

// 打开文件夹
const openDirectory = async () => {
  emit('open-directory'); // 只触发事件，让父组件处理文件夹选择
};

// 开始文件夹扫描和缩略图生成
const startFolderScan = async (directoryPath) => {
  if (!window.electron) {
    ElMessage.error('无法访问Electron API');
    return;
  }

  // 确保directoryPath是字符串
  const dirPath = typeof directoryPath === 'string' ? directoryPath : directoryPath.directory;
  if (!dirPath) {
    ElMessage.error('无效的目录路径');
    return;
  }

  loading.value = true;
  try {
    // 获取缩略图路径
    const settingsResult = await window.electron.invoke('load-settings');
    
    // 使用与App.vue相同的参数结构
    const result = await window.electron.invoke('start-folder-scan', {
      directoryPath: dirPath,
      settings: {
        thumbnailPath: settingsResult.success ? settingsResult.data.basic.thumbnailPath : undefined,
        scanDirectory: dirPath // 确保传递scanDirectory参数
      }
    });
    
    if (!result.success) {
      ElMessage.error(result.error || '启动文件夹扫描失败');
    }
  } catch (error) {
    console.error('启动文件夹扫描失败:', error);
    ElMessage.error('启动文件夹扫描失败');
  } finally {
    loading.value = false;
  }
};

// 懒加载节点
const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    // 根节点
    if (!props.directory) {
      resolve([]);
      return;
    }
    
    // 确保directory是字符串
    const dirPath = typeof props.directory === 'string' ? props.directory : props.directory.directory;
    if (!dirPath) {
      resolve([]);
      return;
    }
    
    resolve([{
      label: dirPath.split(/[\\/]/).pop(),
      path: dirPath,
      type: 'directory'
    }]);
    expandedKeys.value.push(dirPath);
    return;
  }
  
  if (!window.electron) {
    ElMessage.error('无法访问Electron API');
    resolve([]);
    return;
  }

  try {
    // 使用IPC服务获取目录列表
    const result = await window.electron.invoke('list-images', {
      params: {
        directoryPath: node.data.path
      }
    });
    
    if (result.success && result.data.images) {
      const files = result.data.images.map(file => ({
        label: file.name,
        path: file.path,
        type: file.type,
        isLeaf: file.type !== 'directory'
      }));
      
      resolve(files);
    } else {
      resolve([]);
    }
  } catch (error) {
    console.error('加载目录内容失败:', error);
    ElMessage.error('加载目录内容失败');
    resolve([]);
  }
};

// 点击节点
const handleNodeClick = (data) => {
  if (data.type === 'file' && isImage(data)) {
    selectedPath.value = data.path;
    emit('select-file', data.path);
  }
};

// 刷新目录
const refreshDirectory = () => {
  if (!props.directory) return;
  
  // 保存当前根路径
  const dirPath = typeof props.directory === 'string' ? props.directory : props.directory.directory;
  if (!dirPath) return;
  
  // 保存当前展开的节点
  const currentExpandedKeys = [...expandedKeys.value];
  
  // 清空当前状态
  selectedPath.value = '';
  expandedKeys.value = [];
  
  // 重置树结构
  if (fileTreeRef.value) {
    // 如果是懒加载树，使用store API重置数据
    if (fileTreeRef.value.store) {
      fileTreeRef.value.store.setData([]);
    }
    
    // 清空当前选择
    fileTreeRef.value.setCurrentKey(null);
    
    // 重建根节点数据
    fileTreeData.value = [{
      label: dirPath.split(/[\\/]/).pop(),
      path: dirPath,
      type: 'directory'
    }];
    
    // 确保根节点仍然展开
    expandedKeys.value = [dirPath];
    
    // 给DOM一点时间更新
    nextTick(() => {
      // 在根节点重建后启动文件夹扫描以更新缩略图
      startFolderScan(dirPath);
    });
  } else {
    // 如果树组件引用不存在，至少更新缩略图
    startFolderScan(dirPath);
  }
  
  // 通知用户刷新已启动
  ElMessage.success('文件夹刷新已启动');
};

// 监听目录变化
watch(() => props.directory, () => {
  if (props.directory) {
    refreshDirectory();
  }
});
</script>

<style scoped>
.file-explorer-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.explorer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.explorer-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--text-color); /* 使用变量 */
}

.header-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: var(--text-color-secondary); /* 使用变量 */
}

.file-tree-container {
  flex: 1;
  overflow: auto; /* 保持 auto 以确保滚动功能正常 */
  /* 视觉上隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
}

.file-tree-container::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-icon {
  display: flex;
  align-items: center;
}

.file-name-container {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-name {
  font-size: 14px;
}

/* 鼠标悬停时文件名滚动效果 */
.file-name-container:hover .file-name {
  display: inline-block;
  padding-left: 100%;
  animation: marquee 10s linear infinite;
  white-space: nowrap;
}

@keyframes marquee {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(0); }
}
</style> 