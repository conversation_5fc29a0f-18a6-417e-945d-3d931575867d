# DocImage Processor

一款强大的文档图像处理单机工具，结合Electron前端与JavaCV后端，提供高效的图像处理功能。

## 功能特点

- **高效处理**：采用缩略图预处理机制，提升操作流畅度
- **非破坏性编辑**：在缩略图上进行修改，最终可选择应用到原图或生成新图像
- **硬件加速**：支持GPU加速，提高处理速度
- **批量处理**：支持文件夹批量操作
- **自定义配置**：灵活的设置选项，满足不同需求
- **AI增强**：预留AI接口，未来支持智能化处理

## 关键概念与定义

### 图像标识与处理流程

#### 1. 任务ID (TaskID)
- **定义**：基于图像路径生成的SHA-1哈希值，用于唯一标识处理任务
- **特点**：
  - 唯一性：相同图像内容生成相同的TaskID
  - 简化参数：当前版本仅基于图像路径计算，不包含处理参数
  - 缓存关联：用于索引处理结果缓存
- **应用场景**：
  - 图像处理结果缓存检索
  - 前后端通信中的任务标识
  - 处理历史的引用索引

#### 2. 基准图 (Baseline Image)
- **定义**：功能模块之间切换时保存的中间状态图像
- **目的**：
  - 优化处理流程：避免每次都从原始图像开始处理
  - 增量处理：每个功能模块只需处理其参数变化部分
  - 提高响应速度：缩短复杂编辑链的处理时间
- **工作机制**：
  - 通过会话ID (SessionID) 关联基准图路径和任务ID
  - 切换功能模块时将当前图像状态保存为基准图
  - 下一个功能模块基于此基准图进行处理
- **生命周期**：会话内有效，可配置清理策略

#### 3. 缓存图 (Cached Image)
- **定义**：在内存中保存的处理结果图像
- **存储结构**：
  - 使用 ConcurrentHashMap 存储 TaskID → Mat对象 映射
  - 同时维护访问时间记录用于清理策略
- **优势**：
  - 内存访问：避免磁盘IO，提高响应速度
  - 资源复用：相同参数的处理请求重用结果
  - 自动管理：定期清理长时间未使用的缓存

#### 4. 缩略图 (Thumbnail)
- **定义**：原图的低分辨率版本，用于界面展示和快速预览
- **命名规则**：`{sha1}_thumb.{ext}`，其中sha1为原图内容的哈希值
- **目录结构**：保持与原图相同的相对路径结构
- **生成时机**：首次打开图像或添加到处理队列时

### 图像处理流程

1. **图像加载**：
   - 前端请求加载图像
   - 后端生成/获取缩略图
   - 建立原图与缩略图的关联

2. **编辑操作**：
   - 用户在界面上调整参数
   - 前端生成任务ID并发送请求
   - 后端检查缓存，若存在则返回结果
   - 若不存在，则处理图像并缓存结果

3. **功能模块切换**：
   - 保存当前状态为基准图
   - 关联会话ID与基准图路径
   - 新功能模块基于基准图进行处理

4. **最终保存**：
   - 用户确认后将处理应用至原图
   - 选择覆盖原图或生成新文件
   - 可选择是否保留操作历史

## 技术架构

### 前端 (@frontend)
- **框架**：Electron + Vue 3
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **构建工具**：Vite
- **职责**：图像加载、界面交互、缩略图展示

### 后端
- **核心**：JavaCV
- **职责**：提供强大的图像处理能力
- **通信**：基于文件路径的高效通信机制

### 通信机制
- **模式**：WebSocket 长连接通信
- **优势**：
  - 实时性：双向通信，支持实时进度反馈
  - 功能完备：支持命令发送、进度推送和结果返回
  - 资源效率：后台服务常驻，避免频繁启动JVM的开销
  - 状态保持：长连接可保持会话状态，支持任务取消等交互
  - 安全性高：本地通信，不暴露外部接口
- **工作流程**：
  - Electron主进程启动后台Java服务，并建立WebSocket连接
  - 前端通过IPC向主进程发送处理请求
  - 主进程将请求通过WebSocket发送给Java服务
  - Java服务实时推送处理进度和最终结果
  - 主进程通过IPC将进度和结果转发给渲染进程

### AI组件
- **多模态理解**：Ollama集成（llava等模型）
- **图像处理增强**：
  - 内置JavaCV的传统算法
  - 轻量级预训练模型（ONNX格式）
  - Tesseract OCR引擎集成
- **部署方式**：
  - 简单集成，无需复杂工作流配置
  - 模块化设计，按需加载AI能力
  - 资源友好，适合普通硬件配置

## 功能边界划分

### 前端职责
- 文件系统交互（选择文件/文件夹）
- 缩略图展示与管理（浏览、选择、排序）
- 用户界面与交互设计
- 处理参数配置界面
- 编辑历史管理
- 预览效果展示（基于后端处理后的图像）
- 应用配置管理
- **临时展示性变换**：
  - 预览时的缩放操作
  - 界面交互中的旋转预览
  - 浏览时的平移和简单变换

### 后端职责
- 图像处理核心算法
- 缩略图生成
- 图像分析（尺寸、颜色分布等）
- 批量处理引擎
- 高级图像变换
- 硬件加速管理
- **永久性图像变换**：
  - 高质量缩放（需保存的缩放）
  - 精确角度旋转
  - 裁剪和变形操作
  - 需要保存的所有图像处理操作

### AI模块职责
- 文档内容理解（Ollama多模态模型）
- 文本识别与提取（Tesseract OCR）
- 智能图像增强：
  - 去噪和锐化
  - 自动对比度和亮度调整
  - 文档边界检测和透视校正
- 简单友好的AI功能接口：
  - 预设效果，无需复杂配置
  - 一键式智能处理
  - 可解释的AI建议

### 通信协议
- 前端传递：文件路径、处理参数
- 后端返回：处理后的图像路径、处理状态、元数据
- **WebSocket消息类型**：
  - 命令消息：图像处理操作请求
  - 进度消息：实时处理进度反馈
  - 结果消息：处理完成后的结果
  - 错误消息：异常情况报告
  - 控制消息：任务取消、暂停等控制指令

## 配置选项

### 基础配置
- 缩略图存储路径
- 输出模式（替换原图/新文件）
- 输出文件命名规则

### 性能配置
- GPU加速开关
- 缓存管理设置
- 并行处理线程数

### AI配置
- 本地Ollama模型设置
- 轻量级模型选择
- OCR语言包配置
- 处理强度和优先级设置

## 开发计划

1. 基础架构搭建
   - Electron + Vue 3 + Element Plus环境配置
   - JavaCV集成与WebSocket服务
   - 双层通信机制（前端IPC + 后端WebSocket）

2. 核心功能实现
   - 图像加载与展示
   - 缩略图生成与管理
   - 基础图像处理功能

3. 高级特性
   - 批量处理能力
   - 预设管理
   - AI接口集成：
     - 轻量级模型集成（无需复杂工作流）
     - 基础文档识别功能
     - 智能增强预设

## 使用场景

- 文档扫描后的图像优化
- 批量处理文档图像
- 专业图像编辑需求
- 智能文档分析与处理

## 缩略图生成逻辑

### 1. 缩略图命名规则
- 使用原图内容的 SHA-1 哈希值作为缩略图文件名
- 格式：`{sha1}_thumb.{ext}`
- 示例：`a1b2c3d4e5f6g7h8i9j0_thumb.jpg`

### 2. 目录结构保持
- 缩略图保持原图的目录结构
- 示例：
  - 原图路径：`D:\1\2\3\image.jpg`
  - 扫描目录：`D:\1`
  - 缩略图路径：`{thumbnailDir}\2\3\{sha1}_thumb.jpg`

### 3. 前后台一致性
#### 前台 (main.js)
```javascript
// 1. 读取原图文件内容
const fileBuffer = await fs.readFile(filePath);

// 2. 计算SHA-1哈希值
const hash = crypto.createHash('sha1');
hash.update(fileBuffer);
const fileHash = hash.digest('hex');

// 3. 保持原图目录结构
const relativePath = path.relative(settings.scanDirectory || '', path.dirname(filePath));
const thumbnailFileName = `${fileHash}_thumb${ext}`;
const thumbnailFilePath = path.join(thumbnailDir, relativePath, thumbnailFileName);
```

#### 后台 (ThumbnailGenerator.java)
```java
// 1. 计算文件SHA-1哈希值
String fileHash = calculateFileHash(originalFile);

// 2. 计算相对路径
Path original = Paths.get(originalPath).toAbsolutePath();
Path scan = Paths.get(scanDirectory).toAbsolutePath();
Path relative = scan.relativize(original.getParent());

// 3. 生成缩略图路径
return Paths.get(thumbnailDir, relative.toString(), fileHash + "_thumb" + ext).toString();
```

### 4. 缩略图生成流程
1. **前台处理**:
   - 读取原图文件内容
   - 计算 SHA-1 哈希值
   - 计算相对路径
   - 构建缩略图路径
   - 发送 WebSocket 请求到后台

2. **后台处理**:
   - 接收 WebSocket 请求
   - 验证文件存在性
   - 计算文件 SHA-1 哈希值
   - 计算相对路径
   - 生成缩略图
   - 返回缩略图路径

3. **存储规则**:
   - 缩略图存储在配置的 `thumbnailDir` 目录下
   - 保持原图的目录结构
   - 缩略图路径格式：`file://{thumbnailDir}/{relativePath}/{sha1}_thumb.{ext}`
   - 如果缩略图已存在，直接返回路径
   - 如果不存在，则生成新的缩略图

### 5. 优点
1. **唯一性**：
   - 相同内容的图片生成相同的缩略图
   - 不同内容的图片即使同名也会生成不同的缩略图

2. **一致性**：
   - 前后台使用相同的 SHA-1 算法
   - 确保缩略图与内容强相关

3. **正确性**：
   - 原图路径保持不变
   - 缩略图路径基于内容生成
   - 保持原图目录结构

4. **可维护性**：
   - 缩略图按原图目录结构组织
   - 方便清理和管理
   - 便于批量操作

### 6. 注意事项
1. 缩略图生成过程中：
   - 必须使用原图内容计算 SHA-1
   - 不能使用文件名或路径作为标识符
   - 确保前后台使用相同的 SHA-1 算法
   - 正确计算相对路径

2. 缩略图存储：
   - 缩略图目录必须存在且有写入权限
   - 缩略图文件名必须包含原图扩展名
   - 保持原图目录结构

3. 错误处理：
   - 文件不存在时返回错误
   - SHA-1 计算失败时返回错误
   - 缩略图生成失败时返回错误
   - 目录创建失败时返回错误

### 7. 测试要点
1. 验证相同内容的图片是否生成相同的缩略图
2. 验证不同内容的图片是否生成不同的缩略图
3. 验证原图路径是否保持不变
4. 验证缩略图目录结构是否正确
5. 验证缩略图生成和访问是否正常
6. 验证错误处理是否正常
7. 验证目录权限是否正确

## 图像处理历史管理

### 1. 操作历史设计
- **单缩略图模型**：每张图片只生成一个初始缩略图文件
- **操作序列存储**：使用Electron存储机制保存操作历史
- **非破坏性编辑**：所有编辑操作在缩略图上进行，原图保持不变
- **最终应用**：只在用户确认保存时将操作应用到原图

### 2. 历史记录存储结构
```json
{
  "imageId": "图片路径哈希",
  "originalPath": "原图完整路径",
  "thumbnailPath": "基础缩略图路径",
  "lastModified": "最后修改时间戳",
  "operations": [
    {"type": "旋转", "angle": 90, "timestamp": 123456789},
    {"type": "裁剪", "rect": {"x": 0, "y": 0, "width": 100, "height": 100}, "timestamp": 123456790}
  ],
  "operationStack": [],  // 已执行的操作
  "redoStack": []        // 已撤销但可重做的操作
}
```

### 3. 历史文件命名约定
- **文件位置**：与缩略图保存在相同目录
- **命名规则**：使用与缩略图相同的基础文件名，但更改后缀
- **文件格式**：`.history.json`
- **示例**：
  - 原图: `/path/to/image.jpg`
  - 缩略图: `/thumbnails/path/a1b2c3d4e5f6g7h8i9j0_thumb.jpg`
  - 历史文件: `/thumbnails/path/a1b2c3d4e5f6g7h8i9j0_thumb.history.json`

这种命名约定确保:
- 每个图片有独立的历史记录
- 历史文件与其对应的缩略图紧密关联
- 容易根据缩略图查找对应的历史文件
- 不会与其他文件类型冲突

### 4. 操作流程
1. **初始加载**：
   - 打开图片时生成基础缩略图
   - 检查是否存在历史记录文件
   - 加载历史操作并应用到缩略图

2. **编辑操作**：
   - 用户操作只应用于缩略图
   - 操作参数添加到历史记录
   - 更新Electron存储中的历史文件

3. **图片切换**：
   - 保存当前图片的操作历史
   - 加载新图片的历史记录
   - 应用历史操作到新图片缩略图

4. **最终保存**：
   - 用户确认时将所有操作应用于原图
   - 选择覆盖原图或保存为新文件
   - 可选择是否保留操作历史

### 5. 撤销/重做功能
1. **撤销操作(Undo)**：
   - 从操作栈中移除最后一个操作
   - 将该操作添加到重做栈
   - 从初始缩略图开始重新应用剩余操作

2. **重做操作(Redo)**：
   - 从重做栈中取回操作
   - 将操作添加回操作栈
   - 应用该操作到当前缩略图状态

3. **UI集成**：
   - 工具栏中添加撤销/重做按钮
   - 支持标准快捷键(Ctrl+Z/Ctrl+Y)
   - 根据栈状态动态启用/禁用按钮

### 6. 资源管理策略
1. **内存管理**：
   - 活动图片的缩略图保留在内存中
   - 非活动图片只保存操作参数，不保留图像数据
   - 设置缓存上限，超出时清理最早访问的图片

2. **磁盘管理**：
   - 历史记录文件存储在应用数据目录
   - 定期清理长时间未访问的历史记录
   - 提供清理缓存的选项

### 7. 实现优势
1. **高效资源利用**：不生成大量中间缩略图文件
2. **完整历史追踪**：支持无限制的撤销/重做
3. **会话持久性**：应用重启后仍可恢复编辑状态
4. **非破坏性工作流**：原图始终保持不变，直到用户确认保存

## 图像处理缓存系统

### 1. 缓存设计原理
- 基于 SHA-1 哈希的内存缓存系统
- 前端/后端一致的任务ID生成机制
- 缓存内容：处理后的 OpenCV Mat 对象
- 自动清理：定期清理长时间未使用的缓存

### 2. 任务ID生成规则
#### 后端 (Java)
```java
public static String createTaskId(String imagePath, Object... params) {
    // 创建字符串构建器，首先添加图像路径
    StringBuilder builder = new StringBuilder(imagePath);
    
    // 添加所有参数，确保浮点数格式一致性
    for (Object param : params) {
        if (param instanceof Double || param instanceof Float) {
            // 对浮点数使用固定小数位格式
            double value = ((Number) param).doubleValue();
            builder.append(String.format("%.1f", value));
        } else {
            builder.append(param.toString());
        }
    }
    
    // 计算SHA-1哈希并返回
    return calculateStringHash(builder.toString());
}
```

#### 前端 (JavaScript)
```javascript
static createTaskId(imagePath, brightness, contrast, saturation, sharpness) {
  // 构建参数字符串 - 直接追加参数而不使用分隔符
  let paramsStr = imagePath;
  
  // 添加所有参数，对浮点数进行格式化处理
  const params = [brightness, contrast, saturation, sharpness];
  for (const param of params) {
    if (typeof param === 'number') {
      // 对浮点数使用固定格式，与后端保持一致
      paramsStr += param.toFixed(1);
    } else if (param !== undefined && param !== null) {
      paramsStr += param.toString();
    }
  }
  
  // 计算SHA-1哈希
  return this.calculateStringHash(paramsStr);
}
```

### 3. 缓存系统优势
- **减少磁盘IO**：不再为每次参数调整生成临时文件
- **更快的响应体验**：直接返回Base64图像数据，而不是文件路径 
- **资源高效利用**：相同参数的处理请求只需处理一次
- **自动内存管理**：定时清理未使用的缓存项，避免内存泄漏
- **提升用户体验**：撤销/重做操作更快，实时预览更流畅

### 4. 实现架构
#### 后端 (Java)
```java
public class ImageProcessingCache {
    // 图像缓存：TaskId -> Mat对象
    private final Map<String, Mat> imageCache = new ConcurrentHashMap<>();
    
    // 最后访问时间：TaskId -> 时间戳
    private final Map<String, Long> lastAccessTime = new ConcurrentHashMap<>();
    
    // 从缓存获取结果
    public Mat getFromCache(String taskId) {
        if (hasCache(taskId)) {
            lastAccessTime.put(taskId, System.currentTimeMillis());
            return imageCache.get(taskId).clone();
        }
        return null;
    }
    
    // 缓存处理结果
    public void putToCache(String taskId, Mat image) {
        imageCache.put(taskId, image.clone());
        lastAccessTime.put(taskId, System.currentTimeMillis());
    }
    
    // 定期清理过期缓存项
    private void cleanupCache() {
        // 清理10分钟未访问的缓存项
    }
}
```

#### 前端 (Vue)
```javascript
// 发送处理请求并获取处理结果
async function processImage(imagePath, params) {
  // 1. 使用相同的算法生成TaskId
  const taskId = TaskIdUtils.createTaskId(
    imagePath, 
    params.brightness, 
    params.contrast, 
    params.saturation, 
    params.sharpness
  );
  
  // 2. 发送处理请求
  const result = await window.electron.invoke('preview-image-edit', {
    filePath: imagePath,
    params: params,
    taskId: taskId
  });
  
  // 3. 使用taskId获取图像数据
  if (result.success) {
    const imageData = await window.electron.invoke('get-cached-image', {
      taskId: taskId,
      quality: 90
    });
    
    // 4. 使用Base64显示图像
    if (imageData.success) {
      imageUrl.value = `data:image/jpeg;base64,${imageData.imageData}`;
    }
  }
}
```

### 5. 通信流程
1. **前端**: 计算任务ID并发送图像路径和处理参数到Electron主进程
2. **主进程**: 通过WebSocket发送请求到Java后端
3. **后端**: 
   - 验证或重新计算任务ID (SHA1哈希)
   - 检查缓存，如存在则直接返回结果
   - 不存在则处理图像并缓存结果
   - 将结果编码为Base64并返回给前端
4. **前端**:
   - 直接使用返回的Base64图像数据
   - 不再需要读取临时文件

### 6. 内存管理策略
- **缓存过期时间**: 10分钟未访问的缓存项会被清理
- **资源释放**: OpenCV Mat对象使用后及时调用release()方法释放
- **定期清理**: 后台每分钟执行一次缓存清理
- **引用计数**: 使用clone()方法避免多处引用同一个Mat对象

### 7. 优化效果
- **减少临时文件**: 不再为每次参数调整生成临时文件
- **提高交互流畅度**: 相同参数的调整请求直接从缓存获取结果
- **降低服务器负载**: 重复请求通过缓存处理，减轻处理压力
- **降低网络传输**: 只在必要时传输图像数据 