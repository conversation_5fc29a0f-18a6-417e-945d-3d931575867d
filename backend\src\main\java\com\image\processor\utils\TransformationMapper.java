package com.image.processor.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_core.Rect;

/**
 * 图像变换映射器
 * 负责处理缩略图与原图之间的坐标映射关系
 */
public class TransformationMapper {
    private static final Logger logger = LoggerFactory.getLogger(TransformationMapper.class);
    
    // 缩略图的最大尺寸
    public static final int THUMBNAIL_MAX_DIMENSION = 1200;
    
    /**
     * 计算原图到缩略图的缩放比例
     * 
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 缩放比例
     */
    public static double calculateScaleRatio(int originalWidth, int originalHeight) {
        if (originalWidth <= THUMBNAIL_MAX_DIMENSION && originalHeight <= THUMBNAIL_MAX_DIMENSION) {
            // 原图尺寸小于缩略图最大尺寸，不需要缩放
            return 1.0;
        }
        
        // 基于最大维度计算缩放比例
        if (originalWidth >= originalHeight) {
            // 横图，宽度缩放到最大尺寸
            return (double) THUMBNAIL_MAX_DIMENSION / originalWidth;
        } else {
            // 竖图，高度缩放到最大尺寸
            return (double) THUMBNAIL_MAX_DIMENSION / originalHeight;
        }
    }
    
    /**
     * 计算缩略图尺寸
     * 
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 缩略图尺寸 [width, height]
     */
    public static int[] calculateThumbnailDimensions(int originalWidth, int originalHeight) {
        double ratio = calculateScaleRatio(originalWidth, originalHeight);
        
        int thumbnailWidth = (int) Math.round(originalWidth * ratio);
        int thumbnailHeight = (int) Math.round(originalHeight * ratio);
        
        return new int[] { thumbnailWidth, thumbnailHeight };
    }
    
    /**
     * 将缩略图上的坐标映射到原图坐标
     * 
     * @param thumbnailX 缩略图X坐标
     * @param thumbnailY 缩略图Y坐标
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 原图上的坐标
     */
    public static Point mapToOriginal(int thumbnailX, int thumbnailY, 
                                     int originalWidth, int originalHeight) {
        double ratio = calculateScaleRatio(originalWidth, originalHeight);
        
        // 缩略图坐标除以缩放比例得到原图坐标
        int originalX = (int) Math.round(thumbnailX / ratio);
        int originalY = (int) Math.round(thumbnailY / ratio);
        
        logger.debug("映射坐标: 缩略图({},{}) -> 原图({},{}), 比例:{}", 
                   thumbnailX, thumbnailY, originalX, originalY, ratio);
        
        return new Point(originalX, originalY);
    }
    
    /**
     * 将原图上的坐标映射到缩略图坐标
     * 
     * @param originalX 原图X坐标
     * @param originalY 原图Y坐标
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 缩略图上的坐标
     */
    public static Point mapToThumbnail(int originalX, int originalY, 
                                      int originalWidth, int originalHeight) {
        double ratio = calculateScaleRatio(originalWidth, originalHeight);
        
        // 原图坐标乘以缩放比例得到缩略图坐标
        int thumbnailX = (int) Math.round(originalX * ratio);
        int thumbnailY = (int) Math.round(originalY * ratio);
        
        logger.debug("映射坐标: 原图({},{}) -> 缩略图({},{}), 比例:{}", 
                   originalX, originalY, thumbnailX, thumbnailY, ratio);
        
        return new Point(thumbnailX, thumbnailY);
    }
    
    /**
     * 将缩略图上的矩形区域映射到原图
     * 
     * @param thumbnailRect 缩略图上的矩形区域
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 原图上的矩形区域
     */
    public static Rect mapRectToOriginal(Rect thumbnailRect,
                                        int originalWidth, int originalHeight) {
        double ratio = calculateScaleRatio(originalWidth, originalHeight);
        
        // 缩略图矩形坐标除以缩放比例得到原图矩形
        int x = (int) Math.round(thumbnailRect.x() / ratio);
        int y = (int) Math.round(thumbnailRect.y() / ratio);
        int width = (int) Math.round(thumbnailRect.width() / ratio);
        int height = (int) Math.round(thumbnailRect.height() / ratio);
        
        logger.debug("映射矩形: 缩略图({},{},{},{}) -> 原图({},{},{},{})",
                   thumbnailRect.x(), thumbnailRect.y(), thumbnailRect.width(), thumbnailRect.height(),
                   x, y, width, height);
        
        return new Rect(x, y, width, height);
    }
    
    /**
     * 将原图上的矩形区域映射到缩略图
     * 
     * @param originalRect 原图上的矩形区域
     * @param originalWidth 原图宽度
     * @param originalHeight 原图高度
     * @return 缩略图上的矩形区域
     */
    public static Rect mapRectToThumbnail(Rect originalRect,
                                         int originalWidth, int originalHeight) {
        double ratio = calculateScaleRatio(originalWidth, originalHeight);
        
        // 原图矩形坐标乘以缩放比例得到缩略图矩形
        int x = (int) Math.round(originalRect.x() * ratio);
        int y = (int) Math.round(originalRect.y() * ratio);
        int width = (int) Math.round(originalRect.width() * ratio);
        int height = (int) Math.round(originalRect.height() * ratio);
        
        logger.debug("映射矩形: 原图({},{},{},{}) -> 缩略图({},{},{},{})",
                   originalRect.x(), originalRect.y(), originalRect.width(), originalRect.height(),
                   x, y, width, height);
        
        return new Rect(x, y, width, height);
    }
} 