<template>
  <div class="transform-tools">
    <!-- 裁剪比例选择 -->
    <div class="tool-group">
      <div class="group-header">
        <span class="group-title">裁剪比例</span>
      </div>

      <div class="crop-ratios">
        <el-tooltip content="自由" placement="top">
          <el-button 
            :type="params.cropRatio === 'free' ? 'primary' : 'default'"
            @click="setCropRatio('free')"
            circle
          >
            <el-icon><Crop /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="1:1" placement="top">
          <el-button 
            :type="params.cropRatio === '1:1' ? 'primary' : 'default'"
            @click="setCropRatio('1:1')"
            circle
          >
            <el-icon><Minus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="4:3" placement="top">
          <el-button 
            :type="params.cropRatio === '4:3' ? 'primary' : 'default'"
            @click="setCropRatio('4:3')"
            circle
          >
            <el-icon><PictureRounded /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="16:9" placement="top">
          <el-button 
            :type="params.cropRatio === '16:9' ? 'primary' : 'default'"
            @click="setCropRatio('16:9')"
            circle
          >
            <el-icon><FullScreen /></el-icon> 
          </el-button>
        </el-tooltip>
         <el-tooltip content="A4" placement="top">
           <el-button 
            :type="params.cropRatio === 'A4' ? 'primary' : 'default'"
            @click="setCropRatio('A4')"
             circle
           >
             <el-icon><Document /></el-icon>
           </el-button>
         </el-tooltip>
      </div>
    </div>

    <!-- 旋转控制 -->
    <div class="tool-group">
      <div class="group-header">
        <span class="group-title">旋转</span>
        <div class="quick-rotate-buttons">
          <el-tooltip content="向左旋转90°" placement="top">
            <el-button 
              circle 
              @click="handleQuickRotate(-90)"
            >
              <el-icon><RefreshLeft /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="向右旋转90°" placement="top">
            <el-button 
              circle 
              @click="handleQuickRotate(90)"
            >
              <el-icon><RefreshRight /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>

      <div class="precise-rotate">
        <div class="slider-header">
          <span>精确旋转</span>
          <el-button 
            text 
            type="primary" 
            @click="resetRotation" 
            :disabled="!rotationAngle && rotationAngle !== 0"
            size="small"
            class="reset-button"
          >
            重置
          </el-button>
        </div>
        <el-slider
          v-model="rotationAngle"
          :min="-45" 
          :max="45" 
          :step="1"
          :format-tooltip="formatRotation"
          @change="handleRotationChange"
          class="rotation-slider"
          :marks="{
             '-45': '-45°',
             '0': '0°',
             '45': '45°'
           }"
        />
      </div>
      
      <!-- 自动纠偏占位符 -->
      <!-- 
      <div class="auto-straighten">
        <el-button icon="MagicStick" disabled>自动纠偏 (待开发)</el-button>
      </div> 
      -->
    </div>

  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { 
  Crop, 
  Minus,
  PictureRounded, 
  FullScreen, 
  Document,
  RefreshLeft,
  RefreshRight
} from '@element-plus/icons-vue';

const props = defineProps({
  params: { // 接收父组件传递的参数，包含 currentTool, cropRatio, rotation 等
    type: Object,
    required: true
  },
  id: {
    type: Number,
    default: 2
  }
});

const emit = defineEmits(['set-tool', 'update-params', 'rotate']); // 定义组件可以触发的事件

// --- 状态管理 ---
const rotationAngle = ref(0); // 用于精确旋转滑块的本地状态

// 计算属性，用于将本地旋转角度映射到父组件
const internalRotation = computed(() => props.params.rotation || 0);

// --- 方法 --- 

// 设置裁剪比例 - 现在会自动激活裁剪工具
const setCropRatio = (ratio) => {
  console.log(`TransformTools: 设置裁剪比例为 ${ratio}`);
  // 总是先确保工具是 'crop'
  emit('set-tool', 'crop'); 
  // 然后更新比例参数
  emit('update-params', { 
    cropRatio: ratio
  });
};

// 处理快速旋转按钮点击
const handleQuickRotate = (degrees) => {
  console.log(`TransformTools: 快速旋转 ${degrees} 度`);
  emit('rotate', degrees);
};

// 处理精确旋转滑块变化
const handleRotationChange = (angle) => {
  console.log(`TransformTools: 精确旋转角度变为 ${angle}`);
  emit('update-params', {
    rotation: angle
  });
};

// 重置旋转角度
const resetRotation = () => {
  console.log('TransformTools: 重置旋转角度');
  rotationAngle.value = 0;
  handleRotationChange(0);
};

// 格式化滑块的 Tooltip 显示
const formatRotation = (val) => {
  return `${val}°`;
};

// --- 监听器 ---

// 监听从父组件传入的参数变化，特别是 rotation，以同步滑块状态
watch(() => props.params.rotation, (newRotation) => {
  const angle = newRotation === undefined ? 0 : newRotation;
  console.log('TransformTools: 接收到父组件 rotation 更新:', angle);
  if (rotationAngle.value !== angle) {
      rotationAngle.value = angle;
  }
}, { immediate: true });

// 监听工具变化，如果取消裁剪工具，可能需要清除裁剪比例状态？（可选）
// watch(() => props.params.currentTool, (newTool) => {
//   if (newTool !== 'crop') {
//      // 可以考虑在这里清除 cropRatio
//      // emit('update-params', { cropRatio: null }); 
//   }
// });

</script>

<style scoped>
.transform-tools {
  padding: 0.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* 增加组间距 */
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 让标题和按钮分开 */
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.group-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.crop-ratios {
  display: flex;
  gap: 8px;
  justify-content: space-around; /* 分散对齐 */
  flex-wrap: wrap; /* 允许换行 */
}

.crop-ratios .el-button {
   min-width: 32px; /* 确保圆形按钮有足够空间 */
}

.quick-rotate-buttons {
  display: flex;
  gap: 0.5rem;
}

.precise-rotate {
  padding: 0 0.5rem; /* 给滑块左右留些空间 */
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: -5px; /* 减少与滑块的间距 */
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.reset-button {
  padding: 0; /* 移除文字按钮的内边距 */
}

.rotation-slider {
  /* --el-slider-height: 4px; */ /* 可以调整滑块高度 */
  /* --el-slider-button-size: 14px; */ /* 调整滑块按钮大小 */
}

/* 使marks文字小一点 */
:deep(.el-slider__marks-text) {
  font-size: 11px;
  margin-top: 8px; 
}

/* 调整按钮内图标和文字间距 */
:deep(.el-button > span) {
  gap: 5px;
}

/* 确保圆形按钮是正圆 */
:deep(.el-button.is-circle) {
  padding: 8px;
  border-radius: 50%;
}
</style> 