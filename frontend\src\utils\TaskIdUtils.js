/**
 * 任务工具类 - 浏览器兼容版本
 * 提供任务ID生成和管理功能
 */

/**
 * 任务ID工具类
 */
class TaskIdUtils {
  /**
   * 创建标准化任务ID
   * @param {string} imagePath 图像路径
   * @returns {string} 标准化任务ID
   */
  static createTaskId(imagePath) {
    // 规范化路径
    const normalizedPath = this.normalizePath(imagePath);
    
    // 直接使用图像路径计算哈希值
    return this.calculateStringHash(normalizedPath);
  }
  
  /**
   * 计算字符串哈希值（浏览器兼容版本，使用Web Crypto API实现SHA-1）
   * @param {string} input 输入字符串
   * @returns {string} SHA-1哈希值
   */
  static calculateStringHash(input) {
    try {
      // 如果可以使用浏览器的Web Crypto API
      if (window.crypto && window.crypto.subtle) {
        // 转换字符串为Uint8Array (UTF-8编码，与Java端一致)
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        
        // 创建一个异步计算的Promise
        const hashPromise = crypto.subtle.digest('SHA-1', data).then(hashBuffer => {
          // 转换为16进制字符串
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        });
        
        // 提示: 这实际上是一个Promise，需要异步处理
        console.warn('SHA-1哈希计算是异步的，返回同步结果可能会导致不一致。考虑将API改为异步');
        
        // 因为原API是同步的，我们使用以下方法保持同步API兼容性
        // 注意：这种方法在现代浏览器中不推荐使用，但为了保持API兼容性暂时采用
        
        // 使用替代方法 (djb2算法) 作为同步返回
        // 为了安全起见，保留这个作为备用
        let hash = 5381;
        for (let i = 0; i < input.length; i++) {
          hash = ((hash << 5) + hash) + input.charCodeAt(i); // hash * 33 + c
        }
        
        // 转换为16进制字符串并填充到40位（与SHA-1输出长度一致）
        const hexHash = (hash >>> 0).toString(16).padStart(40, '0');
        
        // 添加计算结果到控制台，以便调试
        hashPromise.then(realSha1 => {
          console.log('Web Crypto SHA-1:', realSha1);
          console.log('Fallback hash:', hexHash);
        });
        
        return hexHash;
      } else {
        // 如果不支持Web Crypto API，使用替代算法
        console.warn('Web Crypto API不可用，使用替代哈希算法');
        
        // 使用djb2算法
        let hash = 5381;
        for (let i = 0; i < input.length; i++) {
          hash = ((hash << 5) + hash) + input.charCodeAt(i); // hash * 33 + c
        }
        
        // 转换为16进制字符串并填充到40位（与SHA-1输出长度一致）
        return (hash >>> 0).toString(16).padStart(40, '0');
      }
    } catch (e) {
      console.error('计算哈希值出错:', e);
      
      // 出错时使用备用算法
      let hash = 5381;
      for (let i = 0; i < input.length; i++) {
        hash = ((hash << 5) + hash) + input.charCodeAt(i);
      }
      return (hash >>> 0).toString(16).padStart(40, '0');
    }
  }
  
  /**
   * 规范化路径（统一路径分隔符）
   * @param {string} filePath 文件路径
   * @returns {string} 规范化的路径
   */
  static normalizePath(filePath) {
    if (!filePath) return '';
    
    // 处理 file:// 协议
    if (filePath.startsWith('file://')) {
      filePath = filePath.substring(7);
    }
    
    // 替换所有反斜杠为正斜杠
    let normalizedPath = filePath.replace(/\\/g, '/');
    
    // 处理 Windows 驱动器字母前的斜杠 (/C:/xxx -> C:/xxx)
    if (/^\/[A-Za-z]:\//.test(normalizedPath)) {
      normalizedPath = normalizedPath.substring(1);
    }
    
    return normalizedPath;
  }
  
  /**
   * 提取任务参数
   * @param {string} taskId 任务ID
   * @param {Object} map 任务参数映射
   * @returns {Object|null} 任务参数
   */
  static extractTaskParams(taskId, map) {
    if (!map.has(taskId)) {
      return null;
    }
    
    return map.get(taskId);
  }
}

export default TaskIdUtils;