package com.image.processor.core;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.imageio.ImageIO;

import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.image.processor.utils.TaskIdUtils;

import static org.bytedeco.opencv.global.opencv_imgcodecs.imwrite;


/**
 * 图像处理缓存系统
 * 使用SHA1哈希作为缓存键，存储处理后的图像，避免重复处理
 */
public class ImageProcessingCache {
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessingCache.class);
    
    // 单例实例
    private static ImageProcessingCache instance;
    
    // 图像缓存：只存储基准图和当前处理图像
    private final Map<String, Mat> imageCache = new ConcurrentHashMap<>();
    
    // 源图像缓存：仅用于当前处理的图像
    private final Map<String, Mat> sourceImageCache = new ConcurrentHashMap<>();
    
    // 任务ID到图像路径的映射
    private final Map<String, String> taskToImagePath = new ConcurrentHashMap<>();
    
    // 当前活跃的图像路径
    private String activeImagePath = null;
    
    // 基准图TaskId - 需要特别保护
    private String baselineTaskId = null;
    
    // 转换器
    private final OpenCVFrameConverter.ToMat converterToMat = new OpenCVFrameConverter.ToMat();
    private final Java2DFrameConverter converterToImage = new Java2DFrameConverter();
    
    private ImageProcessingCache() {
        logger.info("图像处理缓存系统已初始化");
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized ImageProcessingCache getInstance() {
        if (instance == null) {
            instance = new ImageProcessingCache();
        }
        return instance;
    }
    
    /**
     * 设置基准图任务ID
     * 基准图是唯一需要持久保存的图像，不会被清理
     * 
     * @param taskId 基准图任务ID
     */
    public void setBaselineTaskId(String taskId) {
        // 如果有旧的基准图但不是当前设置的，清理它
        if (this.baselineTaskId != null && !this.baselineTaskId.equals(taskId)) {
            // 移除旧的基准图
            Mat oldImage = imageCache.remove(this.baselineTaskId);
            if (oldImage != null) {
                oldImage.release();
                logger.info("释放旧的基准图: {}", this.baselineTaskId);
            }
        }
        
        this.baselineTaskId = taskId;
        logger.info("设置新的基准图任务ID: {}", taskId);
    }
    
    /**
     * 创建任务ID 
     * 
     * @param imagePath 图像路径
     * @return 任务ID（SHA1哈希值）
     */
    public String createTaskId(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            logger.error("无法为空路径创建任务ID");
            return null;
        }
        
        // 使用TaskIdUtils统一生成taskId，确保与前端一致
        String taskId = TaskIdUtils.createTaskId(imagePath);
        
        // 保存任务ID与图像路径的关联，用于后续参考
        if (taskId != null) {
            taskToImagePath.put(taskId, imagePath);
            logger.debug("为原图路径生成taskId: {} -> {}", imagePath, taskId);
        }
        
        return taskId;
    }
    
    /**
     * 创建任务ID - 重载方法（兼容性保持）
     * 注意：此方法忽略所有参数，只使用图像路径生成任务ID
     * 
     * @param imagePath 图像路径（原图）
     * @param brightness 亮度参数（被忽略）
     * @param contrast 对比度参数（被忽略）
     * @param saturation 饱和度参数（被忽略）
     * @param sharpness 锐化参数（被忽略）
     * @return 任务ID（SHA1哈希值）
     */
    public String createTaskId(String imagePath, double brightness, double contrast, 
                              double saturation, double sharpness) {
        // 忽略所有参数，只使用图像路径
        return createTaskId(imagePath);
    }
    
    /**
     * 获取图像的基础ID（只基于路径）
     */
    private String getBaseImageId(String imagePath) {
        // 使用TaskIdUtils计算路径的哈希值
        String normalizedPath = TaskIdUtils.normalizePath(imagePath);
        String hash = TaskIdUtils.calculateStringHash(normalizedPath);
        
        if (hash != null) {
            return hash;
        } else {
            return String.valueOf(imagePath.hashCode());
        }
    }
    
    /**
     * 查找使用相同图像的现有任务
     */
    private String findExistingTaskForImage(String imagePath) {
        for (Map.Entry<String, String> entry : taskToImagePath.entrySet()) {
            if (imagePath.equals(entry.getValue()) && imageCache.containsKey(entry.getKey())) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    /**
     * 计算文件内容的SHA1哈希值
     * 
     * @param filePath 文件路径
     * @return SHA1哈希值，失败返回null
     */
    public String calculateFileHash(String filePath) {
        return TaskIdUtils.calculateFileHash(filePath);
    }
    
    /**
     * 设置当前活跃的图像路径，此图像相关的缓存将被优先保留
     */
    public void setActiveImagePath(String imagePath) {
        if (imagePath != null && !imagePath.equals(this.activeImagePath)) {
            logger.info("设置活跃图像: {}", imagePath);
            this.activeImagePath = imagePath;
        }
    }
    
    /**
     * 获取当前活跃的图像路径
     * 
     * @return 当前活跃的图像路径
     */
    public String getActiveImagePath() {
        return this.activeImagePath;
    }
    
    /**
     * 获取或加载源图像
     * 
     * @param filePath 图像文件路径
     * @return 源图像的Mat对象，失败返回null
     */
    public Mat getOrLoadSourceImage(String filePath) {
        // 自动设置为活跃图像
        setActiveImagePath(filePath);
        
        // 计算文件内容的哈希值作为缓存键
        String fileHash = calculateFileHash(filePath);
        if (fileHash == null) {
            logger.error("无法计算文件哈希值: {}", filePath);
            return null;
        }
        
        // 检查缓存
        Mat cachedImage = sourceImageCache.get(fileHash);
        if (cachedImage != null && !cachedImage.empty()) {
            logger.debug("从源图像缓存获取图像: {}", filePath);
            return cachedImage; // 返回相同引用，不创建副本
        }
        
        // 加载图像
        Mat newImage = loadImage(filePath);
        if (newImage != null) {
            // 存入缓存
            sourceImageCache.put(fileHash, newImage);
            logger.debug("已加载并缓存源图像: {}, 当前源图像缓存大小: {}", filePath, sourceImageCache.size());
        }
        
        return newImage; // 返回引用，不创建副本
    }
    
    /**
     * 从缓存获取图像
     * 
     * @param taskId 任务ID，应由原图路径而非缩略图路径生成
     * @return Mat对象，不存在返回null
     */
    public Mat getFromCache(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        // 检查是否在缓存中
        Mat cachedImage = imageCache.get(taskId);
        if (cachedImage != null) {
            boolean saved = imwrite("test.jpg", cachedImage);
            System.out.println(saved);
            logger.debug("从缓存获取图像: {}", taskId);
            return cachedImage.clone(); // 返回克隆以避免外部修改
        }
        
        return null;
    }
    
    /**
     * 基于任务ID和参数查询缓存结果
     * 针对带参数的查询（过渡方法）
     * 
     * @param taskId 任务ID
     * @param brightness 亮度
     * @param contrast 对比度
     * @param saturation 饱和度
     * @param sharpness 锐化度
     * @return 处理结果Mat对象（如果不存在返回null）
     */
    public Mat getCacheResult(String taskId, double brightness, double contrast, 
                             double saturation, double sharpness) {
        // 由于我们现在只使用图像路径创建taskId，所以直接返回基于taskId的缓存
        // 忽略所有参数
        return getFromCache(taskId);
    }
    
    /**
     * 存入缓存
     * 
     * @param taskId 任务ID，应由原图路径而非缩略图路径生成
     * @param image Mat对象
     */
    public void putToCache(String taskId, Mat image) {
        if (taskId == null || image == null || image.empty()) {
            return;
        }
        
        // 记录任务ID与图像路径的关联
        if (activeImagePath != null) {
            taskToImagePath.put(taskId, activeImagePath);
        }
        
        // 清理非基准图的缓存
        clearNonBaselineCache();
        
        // 存入缓存，如果是基准图则特别标记
        Mat cachedImage = image.clone(); // 存储克隆以避免外部修改
        imageCache.put(taskId, cachedImage);
        
        if (taskId.equals(baselineTaskId)) {
            logger.info("已缓存基准图: {}", taskId);
        } else {
            logger.debug("已缓存图像: {}", taskId);
        }
    }
    
    /**
     * 清理非基准图的缓存
     * 仅保留基准图和当前正在处理的图像
     */
    private void clearNonBaselineCache() {
        // 仅清理非基准图的缓存
        for (String taskId : new HashSet<>(imageCache.keySet())) {
            // 跳过基准图
            if (taskId != null && taskId.equals(baselineTaskId)) {
                continue;
            }
            
            // 跳过与当前活跃图像直接关联的taskId
            String imagePath = taskToImagePath.get(taskId);
            if (activeImagePath != null && activeImagePath.equals(imagePath)) {
                continue;
            }
            
            // 其他缓存可以清理
            Mat oldImage = imageCache.remove(taskId);
            if (oldImage != null) {
                oldImage.release();
                logger.debug("清理非基准图缓存: {}", taskId);
            }
        }
        
        // 清理源图像缓存
        cleanupSourceImageCache();
    }
    
    /**
     * 检查缓存中是否有指定任务ID的图像
     * 
     * @param taskId 任务ID
     * @return 是否存在缓存
     */
    public boolean hasCache(String taskId) {
        boolean exists = imageCache.containsKey(taskId);
        if (exists) {
            logger.debug("找到缓存的处理结果: {}", taskId);
        }
        return exists;
    }
    
    /**
     * 切换活跃图像时清理所有非基准图缓存
     */
    public void onImageSwitch() {
        // 清理所有源图像缓存
        for (Mat image : sourceImageCache.values()) {
            if (image != null) {
                image.release();
            }
        }
        sourceImageCache.clear();
        
        // 清理所有非基准图的缓存
        for (String taskId : new HashSet<>(imageCache.keySet())) {
            // 保留基准图
            if (taskId != null && taskId.equals(baselineTaskId)) {
                continue;
            }
            
            Mat oldImage = imageCache.remove(taskId);
            if (oldImage != null) {
                oldImage.release();
            }
        }
        
        // 清理任务ID到路径的映射
        taskToImagePath.clear();
        
        logger.info("图像切换：已清理所有非基准图缓存");
    }
    
    /**
     * 清理所有源图像缓存
     * 源图像仅在处理过程中需要，可以随时清理
     */
    private void cleanupSourceImageCache() {
        // 源图像可以完全清理，除非是当前正在处理的图像
        if (activeImagePath != null) {
            String activeImageHash = calculateFileHash(activeImagePath);
            
            // 保留活跃图像的源缓存，清理其他所有
            for (String hash : new HashSet<>(sourceImageCache.keySet())) {
                if (!hash.equals(activeImageHash)) {
                    Mat oldImage = sourceImageCache.remove(hash);
                    if (oldImage != null) {
                        oldImage.release();
                    }
                }
            }
        } else {
            // 没有活跃图像，清理所有源图像缓存
            for (Mat image : sourceImageCache.values()) {
                if (image != null) {
                    image.release();
                }
            }
            sourceImageCache.clear();
        }
    }
    
    /**
     * 加载图像到Mat对象
     * 
     * @param filePath 图像文件路径
     * @return 加载的Mat对象，失败返回null
     */
    public Mat loadImage(String filePath) {
        File file = new File(filePath);
        
        // 检查文件是否存在和可读
        if (!file.exists() || !file.isFile() || !file.canRead()) {
            logger.error("无法读取图像文件: {}", filePath);
            return null;
        }
        
        try {
            logger.debug("开始加载图像: {}", filePath);
            BufferedImage bufferedImage = ImageIO.read(file);
            
            if (bufferedImage == null) {
                logger.error("Java ImageIO无法读取图像: {}", filePath);
                return null;
            }
            
            // 将BufferedImage转换为OpenCV的Mat
            Mat image = converterToMat.convert(converterToImage.convert(bufferedImage));
            
            if (image == null || image.empty()) {
                logger.error("无法将图像转换为Mat格式: {}", filePath);
                return null;
            }
            
            logger.debug("成功加载图像: {}", filePath);
            return image;
            
        } catch (IOException e) {
            logger.error("读取图像时出错: {}", filePath, e);
            return null;
        }
    }
    
    /**
     * 将Mat图像保存为JPEG格式的字节数组
     * 
     * @param image Mat图像
     * @param quality JPEG质量（0-100）
     * @return 字节数组，失败返回null
     */
    public byte[] matToJpegBytes(Mat image, int quality) {
        if (image == null || image.empty()) {
            logger.error("无效的图像数据");
            return null;
        }
        
        try {
            // Mat -> Java BufferedImage
            BufferedImage bufferedImage = converterToImage.convert(converterToMat.convert(image));
            
            if (bufferedImage == null) {
                logger.error("无法将Mat转换为BufferedImage");
                return null;
            }
            
            // BufferedImage -> byte[]
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpeg", outputStream);
            
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            logger.error("将Mat转换为JPEG字节数组时出错", e);
            return null;
        }
    }
    
    /**
     * 返回当前缓存使用情况
     */
    public String getCacheStats() {
        return String.format("处理结果缓存: %d项, 源图像缓存: %d项, 活跃图像: %s", 
                           imageCache.size(), sourceImageCache.size(), 
                           activeImagePath != null ? activeImagePath : "无");
    }
    
    /**
     * 释放资源
     */
    public void shutdown() {
        // 释放所有缓存的Mat
        for (Mat mat : imageCache.values()) {
            if (mat != null) {
                mat.release();
            }
        }
        
        for (Mat mat : sourceImageCache.values()) {
            if (mat != null) {
                mat.release();
            }
        }
        
        imageCache.clear();
        sourceImageCache.clear();
        taskToImagePath.clear();
        logger.info("图像处理缓存系统已关闭");
    }
    
    /**
     * 标准化图像路径，移除操作系统差异和不必要的分隔符
     * 
     * @param imagePath 原始图像路径
     * @return 标准化的路径
     */
    private String normalizeImagePath(String imagePath) {
        if (imagePath == null) {
            return "";
        }
        
        // 替换所有反斜杠为正斜杠
        String normalized = imagePath.replace('\\', '/');
        
        // 移除多余的正斜杠
        while (normalized.contains("//")) {
            normalized = normalized.replace("//", "/");
        }
        
        // 转为小写（在不区分大小写的文件系统上）
        normalized = normalized.toLowerCase();
        
        logger.debug("标准化路径: {} -> {}", imagePath, normalized);
        return normalized;
    }
    
    /**
     * 获取所有缓存的任务ID
     * 
     * @return 任务ID集合
     */
    public Set<String> getAllTaskIds() {
        return new HashSet<>(imageCache.keySet());
    }
} 