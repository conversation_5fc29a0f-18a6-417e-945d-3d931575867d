/**
 * 图像处理程序
 * 负责图像处理相关的IPC通信处理
 */

import { ipcMain } from 'electron';
import { previewImageEdit, getCachedImage,getCachedImageApplyParams, startFolderScan, setBaselineImage } from '../services/image-service.js';
import { generateThumbnail } from '../services/thumbnail-service.js';
import TaskIdUtils from '../utils/TaskIdUtils.js';

/**
 * 注册图像处理相关的IPC处理程序
 */
export function registerImageHandlers() {
  // 预览图像编辑
  ipcMain.handle('preview-image-edit', async (_event, data) => {
    return await previewImageEdit(data);
  });

  // 获取缓存图像
  ipcMain.handle('get-cached-image', async (_event, data) => {
    return await getCachedImage(data);
  });

  // 获取缓存图像基于参数
  ipcMain.handle('get-cached-image-apply-params', async (_event, data) => {
    return await getCachedImageApplyParams(data);
  });

  //设置基准图
  ipcMain.handle('set-baseline-image', async (_event, data) => {
    return await setBaselineImage(data);
  });

  // 计算哈希值
  ipcMain.handle('calculate-hash', async (_event, data) => {
    const { input } = data;
    
    if (!input) {
      return { success: false, error: '输入参数为空' };
    }
    
    try {
      // 使用TaskIdUtils计算哈希
      const hash = TaskIdUtils.calculateStringHash(input);
      return { success: true, hash: hash };
    } catch (error) {
      console.error('计算哈希值出错:', error);
      return { success: false, error: error.message };
    }
  });

  // 开始文件夹扫描
  ipcMain.handle('start-folder-scan', async (_event, data) => {
    return await startFolderScan(data);
  });

  // 获取图像信息
  ipcMain.handle('get-image-info', async (_event, data) => {
    const { requestId, params } = data;
    try {
      // 模拟获取图像信息
      // 在真实实现中，应该使用JavaCV读取图像信息
      return {
        success: true,
        data: {
          width: 1920,
          height: 1080,
          format: 'JPEG',
          size: 1024 * 1024 // 1MB
        }
      };
    }
    catch (error) {
      console.error('获取图像信息失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // 生成缩略图
  ipcMain.handle('generate-thumbnail', async (_event, data) => {
    const { filePath, width = 1200, height = 1200, quality = 85 } = data;
    
    return await generateThumbnail(filePath, { width, height, quality });
  });

  // AI增强
  ipcMain.handle('ai-enhance', async (_event, data) => {
    const { requestId, params } = data;
    const { path: imagePath, options } = params;
    try {
      // 这是模拟实现，实际需要对接AI后端服务
      console.log('AI增强请求:', { imagePath, options });
      
      // 模拟AI增强进度
      for (let i = 0; i <= 100; i += 5) {
        // 发送进度更新
        if (global.mainWindow) {
          global.mainWindow.webContents.send('progress-update', i, 'AI增强处理中...');
        }
        // 延迟一小段时间模拟处理
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // 模拟输出路径
      const outputPath = imagePath.replace(/\.([^\.]+)$/, '_enhanced.$1');
      return {
        success: true,
        data: {
          outputPath,
          suggestions: [
            '建议增加亮度',
            '建议裁剪左侧多余部分',
            '检测到文本区域，可进行OCR'
          ]
        }
      };
    }
    catch (error) {
      console.error('AI增强失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
} 