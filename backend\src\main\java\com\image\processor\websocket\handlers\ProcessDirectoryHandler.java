package com.image.processor.websocket.handlers;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.ProgressCallback;
import com.image.processor.core.ThumbnailGenerator;
import com.image.processor.websocket.TaskInfo;

/**
 * 处理文件夹请求处理器
 */
public class ProcessDirectoryHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public ProcessDirectoryHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "process_directory";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        try {
            // 提取参数
            String directoryPath = request.getString("directoryPath");
            String thumbnailDir = request.getString("thumbnailDir");
            String scanDirectory = request.getString("scanDirectory");
            String taskId = request.getString("taskId");
            
            logger.info("开始处理文件夹请求，参数解析结果：");
            logger.info("taskId: {}", taskId);
            logger.info("directoryPath: {}", directoryPath);
            logger.info("thumbnailDir: {}", thumbnailDir);
            logger.info("scanDirectory: {}", scanDirectory);
            
            // 发送任务接收确认
            sendTaskUpdate(conn, taskId, "accepted", 0, "任务已接受，准备处理");
            
            // 创建并存储任务信息
            TaskInfo taskInfo = new TaskInfo(taskId, conn);
            activeTasks.put(taskId, taskInfo);
            
            // 提交任务到线程池
            taskExecutor.submit(() -> {
                try {
                    // 如果任务已被取消，直接返回
                    if (taskInfo.isCancelled()) {
                        logger.info("任务已被取消，不执行: {}", taskId);
                        return;
                    }
                    
                    // 任务开始
                    sendTaskUpdate(conn, taskId, "started", 0, "开始处理");
                    logger.info("开始处理文件夹: {}", directoryPath);
                    
                    // 创建进度回调
                    final ProgressCallback progressCallback = new ProgressCallback() {
                        @Override
                        public boolean onProgress(int progress, String message) {
                            if (taskInfo.isCancelled()) {
                                return false; // 返回false表示任务已取消，应停止处理
                            }
                            sendTaskUpdate(conn, taskId, "progress", progress, message);
                            return true; // 返回true表示继续处理
                        }
                    };
                    
                    // 创建缩略图生成器
                    ThumbnailGenerator generator = new ThumbnailGenerator();
                    
                    // 执行文件夹处理
                    logger.info("开始调用processDirectory方法");
                    List<ThumbnailGenerator.ThumbnailResult> results = generator.processDirectory(
                        directoryPath, thumbnailDir, scanDirectory, progressCallback
                    );
                    logger.info("processDirectory方法调用完成，处理了 {} 个文件", results.size());
                    
                    // 任务已被取消
                    if (taskInfo.isCancelled()) {
                        logger.info("任务在处理过程中被取消: {}", taskId);
                        sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
                        return;
                    }
                    
                    // 发送结果
                    JSONObject resultData = new JSONObject();
                    JSONArray resultsArray = new JSONArray();
                    for (ThumbnailGenerator.ThumbnailResult result : results) {
                        JSONObject resultObj = new JSONObject();
                        resultObj.put("imagePath", result.getImagePath());
                        resultObj.put("success", result.isSuccess());
                        resultObj.put("message", result.getMessage());
                        resultsArray.put(resultObj);
                    }
                    resultData.put("results", resultsArray);
                    
                    sendTaskUpdate(conn, taskId, "completed", 100, resultData);
                    logger.info("文件夹处理完成: taskId={}, 处理文件数={}", taskId, results.size());
                    
                } catch (Exception e) {
                    logger.error("执行任务时出错: taskId=" + taskId, e);
                    sendTaskUpdate(conn, taskId, "error", 0, "处理出错: " + e.getMessage());
                } finally {
                    // 移除活动任务
                    activeTasks.remove(taskId);
                }
            });
        } catch (JSONException e) {
            logger.error("解析请求参数时出错", e);
            sendError(conn, "解析请求参数失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理文件夹请求时出错", e);
            sendError(conn, "处理请求失败: " + e.getMessage());
        }
    }
} 
 
 