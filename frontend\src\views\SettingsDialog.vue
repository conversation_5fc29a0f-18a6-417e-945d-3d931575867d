<template>
  <el-dialog
    v-model="dialogVisible"
    title="应用设置"
    width="70%" 
    :modal-class="'settings-dialog-modal'" 
    :before-close="handleClose"
    append-to-body 
    draggable
    top="5vh" 
  >
    <div class="settings-container-in-dialog">
      <el-tabs type="border-card">
        <!-- 基础设置 -->
        <el-tab-pane label="基础设置">
          <el-form label-position="top" label-width="100px" :model="basicSettings">
            <el-form-item label="缩略图存储路径">
              <div class="path-select wide-path-select">
                <el-input v-model="basicSettings.thumbnailPath" placeholder="请选择缩略图存储路径" readonly />
                <el-button @click="selectThumbnailPath">
                  <el-icon><Folder /></el-icon>
                  浏览
                </el-button>
              </div>
              <div class="form-tip">指定存储缩略图的文件夹路径。默认在用户数据目录。</div>
            </el-form-item>
            
            <el-form-item>
              <template #label>
                <span>输出模式</span>
                <span class="label-tip">选择处理后的图像保存方式</span>
              </template>
              <el-radio-group v-model="basicSettings.outputMode">
                <el-radio label="replace">替换原图</el-radio>
                <el-radio label="new">新文件 (同目录)</el-radio>
                <el-radio label="newFolder">新文件夹</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="选择输出文件夹" v-if="basicSettings.outputMode === 'newFolder'">
              <div class="path-select wide-path-select">
                 <el-input v-model="basicSettings.outputFolderPath" placeholder="请选择输出文件夹" readonly />
                 <el-button @click="selectOutputFolderPath">
                   <el-icon><Folder /></el-icon>
                   浏览
                 </el-button>
              </div>
              <div class="form-tip">指定处理后文件的保存目录。将使用原文件名保存。</div>
            </el-form-item>
            
            <el-form-item label="输出文件命名规则" v-if="basicSettings.outputMode === 'new'">
              <el-input v-model="basicSettings.outputPattern" placeholder="例如: {name}_processed" />
              <div class="form-tip">
                仅在"新文件 (同目录)"模式下生效。可用变量: {name}, {date}, {time}
              </div>
            </el-form-item>
            
            <el-form-item label="默认图像格式">
              <el-select v-model="basicSettings.defaultFormat">
                <el-option label="JPEG (.jpg)" value="jpg" />
                <el-option label="PNG (.png)" value="png" />
                <el-option label="TIFF (.tiff)" value="tiff" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 性能设置 -->
        <el-tab-pane label="性能设置">
          <el-form label-position="top" label-width="100px" :model="performanceSettings">
            <el-form-item label="GPU加速">
              <el-switch
                v-model="performanceSettings.gpuAcceleration"
                active-text="启用"
                inactive-text="禁用" />
              <div class="form-tip">启用GPU加速可以提高图像处理速度，但可能会增加内存占用</div>
            </el-form-item>
            
            <el-form-item label="缓存大小限制 (MB)">
              <el-slider
                v-model="performanceSettings.cacheSize"
                :min="100"
                :max="5000"
                :step="100"
                show-input />
              <div class="form-tip">设置用于存储临时图像数据的缓存大小</div>
            </el-form-item>
            
            <el-form-item label="并行处理线程数">
              <el-slider
                v-model="performanceSettings.threadCount"
                :min="1"
                :max="16"
                :step="1"
                show-input />
              <div class="form-tip">设置批量处理时使用的线程数量，建议不超过CPU核心数</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 网络设置 (已移除，因为使用IPC) -->
        <!-- <el-tab-pane label="网络设置"> ... </el-tab-pane> -->
        
        <!-- AI设置 -->
        <!-- 
        <el-tab-pane label="AI设置">
          <el-form label-position="top" label-width="100px" :model="aiSettings">
            <el-form-item label="Ollama服务器">
              <el-input v-model="aiSettings.ollamaServer" placeholder="例如: http://localhost:11434" />
              <div class="form-tip">Ollama服务器地址，用于AI增强功能 (如果需要)</div>
            </el-form-item>
            
            <el-form-item label="默认模型">
              <el-select v-model="aiSettings.defaultModel">
                <el-option label="Llava (多模态)" value="llava" />
                <el-option label="Bakllava (增强版多模态)" value="bakllava" />
                <el-option label="Moondream (轻量级)" value="moondream" />
              </el-select>
              <div class="form-tip">用于图像分析和增强的默认AI模型</div>
            </el-form-item>
            
            <el-form-item label="OCR语言">
              <el-select v-model="aiSettings.ocrLanguage" multiple>
                <el-option label="中文简体" value="chi_sim" />
                <el-option label="中文繁体" value="chi_tra" />
                <el-option label="英语" value="eng" />
                <el-option label="日语" value="jpn" />
                <el-option label="韩语" value="kor" />
              </el-select>
              <div class="form-tip">OCR识别支持的语言 (如果使用Tesseract)</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        -->
        
        <!-- 关于 -->
        <el-tab-pane label="关于">
          <div class="about-section">
            <div class="app-title">
              <h2>DocImage Processor</h2>
            </div>
            <p class="version">版本: {{ appVersion }}</p>
            <p class="description">
              一款强大的文档图像处理单机工具，结合Electron前端与JavaCV后端，
              提供高效的图像处理功能。
            </p>
            
            <el-divider />
            
            <h3>技术架构</h3>
            <ul>
              <li><strong>前端:</strong> Electron + Vue 3 + Element Plus</li>
              <li><strong>后端:</strong> JavaCV</li>
              <li><strong>AI组件:</strong> Ollama (可选) + Tesseract OCR (可选)</li>
              <li><strong>通信:</strong> Electron IPC</li>
            </ul>
            
            <el-button type="primary" @click="checkForUpdates">
              <el-icon><RefreshRight /></el-icon>
              检查更新
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存设置</el-button>
        <el-button @click="resetSettings" style="margin-left: 10px;">重置默认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Folder, RefreshRight } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 应用版本
const appVersion = ref('0.1.0');

// --- 设置状态 ---
const basicSettings = reactive({
  thumbnailPath: '',
  outputMode: 'new',
  outputPattern: '{name}_processed',
  defaultFormat: 'jpg',
  outputFolderPath: ''
});
const performanceSettings = reactive({ gpuAcceleration: true, cacheSize: 1000, threadCount: 4 });
// const aiSettings = reactive({ ollamaServer: '', defaultModel: 'llava', ocrLanguage: [] }); // 注释掉 AI 设置状态

// --- 方法 ---

// 选择缩略图路径
const selectThumbnailPath = async () => {
  // @ts-ignore - 假设 electron API 已在 preload 中暴露
  if (window.electron && typeof window.electron.selectDirectory === 'function') {
    try {
      // @ts-ignore
      const path = await window.electron.selectDirectory();
      if (path) {
        basicSettings.thumbnailPath = path.directory;
      }
    } catch (error) {
      ElMessage.error('选择目录失败');
      console.error('选择缩略图目录错误:', error);
    }
  } else {
    ElMessage.error('无法访问文件系统 API');
  }
};

// 新增：选择输出文件夹路径
const selectOutputFolderPath = async () => {
  // @ts-ignore - 假设 electron API 已在 preload 中暴露
  if (window.electron && typeof window.electron.selectDirectory === 'function') {
    try {
      // @ts-ignore
      const path = await window.electron.selectDirectory();
      if (path) {
        basicSettings.outputFolderPath = path;
      }
    } catch (error) {
      ElMessage.error('选择目录失败');
      console.error('选择输出目录错误:', error);
    }
  } else {
    ElMessage.error('无法访问文件系统 API');
  }
};

// 保存设置
const handleSave = async () => {
  const settingsData = {
    basic: JSON.parse(JSON.stringify(basicSettings)),
    performance: JSON.parse(JSON.stringify(performanceSettings)),
    // ai: JSON.parse(JSON.stringify(aiSettings)) // 注释掉 AI 设置保存
  };
   try {
    // @ts-ignore
    if (window.electron && typeof window.electron.saveSettings === 'function') {
      // @ts-ignore
      const result = await window.electron.saveSettings(settingsData);
      if (result.success) {
        ElMessage.success('设置已成功保存');
        dialogVisible.value = false;
      } else {
        ElMessage.error(`保存设置失败: ${result.error || '未知错误'}`);
      }
    } else {
       ElMessage.error('无法访问设置保存 API');
    }
  } catch (error) {
    ElMessage.error('保存设置时出错');
    console.error('保存设置 IPC 调用错误:', error);
  }
};

// 取消/关闭对话框
const handleCancel = () => {
  // TODO: 添加放弃更改提示
  dialogVisible.value = false;
};
const handleClose = (done) => {
  // TODO: 添加放弃更改提示
  dialogVisible.value = false; 
  done();
};

// 重置默认设置
const resetSettings = async () => {
  // TODO: 从主进程获取默认值
  const defaultBasic = { 
    thumbnailPath: 'path/to/default/thumbnails', 
    outputMode: 'new', 
    outputPattern: '{name}_processed', 
    defaultFormat: 'jpg',
    outputFolderPath: ''
  };
  const defaultPerformance = { gpuAcceleration: true, cacheSize: 1000, threadCount: 4 };
  // const defaultAi = { ollamaServer: 'http://localhost:11434', defaultModel: 'llava', ocrLanguage: ['chi_sim', 'eng'] }; // 注释掉 AI 默认值

  Object.assign(basicSettings, defaultBasic);
  Object.assign(performanceSettings, defaultPerformance);
  // Object.assign(aiSettings, defaultAi); // 注释掉 AI 重置
  
  ElMessage.info('已重置为默认设置 (模拟)');
};

// 检查更新
const checkForUpdates = () => {
  ElMessage.info('正在检查更新... (模拟)');
  // @ts-ignore
  if (window.electron && typeof window.electron.checkForUpdates === 'function') { 
    // @ts-ignore
    // window.electron.checkForUpdates(); 
    setTimeout(() => ElMessage.success('当前已是最新版本 (模拟)'), 1500);
  } else {
    ElMessage.error('无法访问更新 API');
  }
};

// 加载设置 (当对话框打开时)
const loadSettings = async () => {
  try {
    // @ts-ignore
    if (window.electron && typeof window.electron.loadSettings === 'function') {
      // @ts-ignore
      const result = await window.electron.loadSettings();
      if (result.success && result.data) {
        Object.assign(basicSettings, { outputFolderPath: '', ...result.data.basic }); 
        Object.assign(performanceSettings, result.data.performance || {});
        // Object.assign(aiSettings, result.data.ai || {}); // 注释掉 AI 设置加载
        console.log('设置已加载 (无 AI):', result.data);
      } else {
        ElMessage.error(`加载设置失败: ${result.error || '未知错误'}`);
      }
    } else {
      ElMessage.error('无法访问设置加载 API');
    }

    // 获取应用版本
    // @ts-ignore
    if (window.electron && typeof window.electron.getAppVersion === 'function') {
       // @ts-ignore
      appVersion.value = await window.electron.getAppVersion() || '0.1.0';
    }
  } catch (error) {
    ElMessage.error('加载设置或版本时出错');
    console.error('加载设置 IPC 调用错误:', error);
  }
};

// 监听对话框可见性变化，加载设置
watch(() => props.visible, (newValue) => {
  if (newValue) {
    loadSettings();
  }
});

</script>

<style scoped>
/* 移除 .settings-container 样式，因为现在在对话框内 */
.settings-container-in-dialog {
  /* 可以添加一些内边距 */
  /* padding: 0 10px; */ 
}

.settings-title {
  /* 标题由 el-dialog 提供 */
  display: none; 
}

.path-select {
  display: flex;
  gap: 10px;
  align-items: center;
}
.path-select .el-input {
  flex: 1;
}

.form-tip {
  color: var(--text-color-secondary); /* 使用全局变量 */
  font-size: 12px;
  margin-top: 5px;
}

/* .settings-actions 移到 #footer 插槽 */
.dialog-footer {
  text-align: right; 
}

.about-section {
  text-align: center;
  padding: 10px 0;
}

.app-title h2 {
  font-size: 20px; /* 缩小标题 */
  margin-bottom: 10px;
  color: var(--active-color); /* 使用全局变量 */
}

.version {
  color: var(--text-color-secondary);
  margin-bottom: 15px;
}

.description {
  margin-bottom: 15px;
  line-height: 1.6;
  color: var(--text-color);
}

.about-section h3 {
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
  color: var(--text-color);
}

.about-section ul {
  text-align: left;
  max-width: 450px;
  margin: 0 auto 20px;
  line-height: 1.8;
  padding-left: 20px;
  color: var(--text-color);
}
.about-section li {
  margin-bottom: 5px;
}

/* 加宽路径选择器 */
.wide-path-select {
  /* 可以根据需要调整 flex 布局或宽度 */
  width: 100%; 
}

/* 标签旁边提示文字的样式 */
.label-tip {
  margin-left: 10px;
  color: var(--text-color-secondary); /* 使用次要文字颜色 */
  font-size: 12px; /* 稍小字体 */
  font-weight: normal; /* 普通字重 */
}
</style>

<style>
/* 对话框全局样式调整 (如果需要) */
.settings-dialog-modal .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 15px;
}
.settings-dialog-modal .el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}
/* 暗色主题下 el-tabs 的适配可能需要更细致的调整 */
.dark-theme .el-tabs--border-card {
  background-color: var(--bg-color-light);
  border: 1px solid var(--border-color);
}
.dark-theme .el-tabs--border-card > .el-tabs__header {
  background-color: var(--bg-color); /* 页签头用更深的背景 */
  border-bottom: 1px solid var(--border-color);
}
.dark-theme .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: var(--text-color-secondary);
  border-right: 1px solid var(--border-color);
}
.dark-theme .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: var(--active-color);
  background-color: var(--bg-color-light);
  border-right-color: var(--border-color);
  border-left-color: var(--border-color);
}
.dark-theme .el-tabs--border-card > .el-tabs__content {
  background-color: var(--bg-color-light);
  color: var(--text-color);
  padding: 20px;
}
.dark-theme .el-form-item__label {
  color: var(--text-color);
}

</style> 