/**
 * 图像编辑历史服务
 * 负责管理图像编辑操作的历史记录、保存和恢复
 * 支持按模块ID分组存储和管理操作历史
 */

import {reactive} from "vue";

  // 操作历史管理
 class HistoryService {
  constructor() {
    // 按模块ID分组的操作历史
    this.moduleOperations = new Map(); // moduleId -> operationStack[]
    this.moduleRedoStacks = new Map(); // moduleId -> redoStack[]
    this.currentModuleId = null; // 当前活动的模块ID
    
    // 兼容旧版本，保留总操作栈
    this.operationStack = [];
    this.redoStack = [];
    
    // 历史记录最大长度（每个模块）
    this.maxHistoryLength = 100;
  }

  /**
   * 设置当前活动的模块ID
   * @param {string|number} moduleId 模块ID
   */
  setCurrentModuleId(moduleId) {
    if (moduleId !== null && moduleId !== undefined) {
      this.currentModuleId = moduleId;
      console.log(`当前活动模块设置为: ${moduleId}`);
    }
  }

  /**
   * 确保模块的操作栈存在
   * @param {string|number} moduleId 模块ID
   * @private
   */
  _ensureModuleStacks(moduleId) {
    if (!this.moduleOperations.has(moduleId)) {
      this.moduleOperations.set(moduleId, []);
    }
    if (!this.moduleRedoStacks.has(moduleId)) {
      this.moduleRedoStacks.set(moduleId, []);
    }
  }

  /**
   * 添加操作到历史
   * @param {Object} operation 操作对象
   * @param {string|number} moduleId 模块ID
   * @returns {Promise<void>}
   */
  async addOperation(operation, moduleId = null) {
    // 使用提供的moduleId或当前模块ID
    const targetModuleId = moduleId || this.currentModuleId || 'default';
    
    // 确保模块栈存在
    this._ensureModuleStacks(targetModuleId);
    
    // 添加时间戳和模块ID
    operation.timestamp = Date.now();
    operation.moduleId = targetModuleId;
    
    // 如果是adjustParams类型，确保记录前参数状态
    if (operation.type === 'adjustParams' && !operation.prevParams) {
      const moduleStack = this.moduleOperations.get(targetModuleId);
      const lastParamOp = [...moduleStack].reverse().find(op => op.type === 'adjustParams');
      // 一定要深拷贝
      operation.prevParams = lastParamOp ? JSON.parse(JSON.stringify(lastParamOp.params)) : {};
    }
    // 一定要深拷贝当前参数
    if (operation.params) {
      operation.params = JSON.parse(JSON.stringify(operation.params));
    }
    
    // 添加到模块的操作栈
    const moduleStack = this.moduleOperations.get(targetModuleId);
    moduleStack.push(operation);
    
    // 限制历史长度
    if (moduleStack.length > this.maxHistoryLength) {
      moduleStack.shift(); // 移除最旧的操作
    }
    
    // 清空模块的重做栈
    this.moduleRedoStacks.set(targetModuleId, []);
    
    // 同时添加到全局操作栈（向后兼容）
    this.operationStack.push(operation);
    this.redoStack = [];

    console.log("操作",moduleStack)
    console.log(`已添加操作: ${operation.type} 到模块 ${targetModuleId}`, operation);
    
    // 如果是编辑缩略图，保存历史
    if (operation.isOriginalImage === false && operation.filePath) {
      await this.saveOperationHistory(operation.filePath);
    }
  }

  /**
   * 撤销指定模块的最后一个操作
   * @param {string|number} moduleId 模块ID（可选，默认为当前模块）
   * @returns {Promise<Object|null>} 撤销的操作
   */
  async undo(moduleId = null) {
    const targetModuleId = moduleId || this.currentModuleId || 'default';
    
    if (!this.canUndo(targetModuleId)) return null;
    
    // 确保模块栈存在
    this._ensureModuleStacks(targetModuleId);
    
    // 从模块操作栈中取出最后一个操作
    const moduleStack = this.moduleOperations.get(targetModuleId);
    const operation = moduleStack.pop();

    // 加入模块重做栈
    const moduleRedoStack = this.moduleRedoStacks.get(targetModuleId);
    moduleRedoStack.push(operation);

    // 同时更新全局栈（向后兼容）
    const globalIndex = this.operationStack.findIndex(op =>
      op === operation ||
      (op.type === operation.type &&
       op.timestamp === operation.timestamp &&
       op.moduleId === operation.moduleId)
    );

    if (globalIndex !== -1) {
      this.operationStack.splice(globalIndex, 1);
    }
    this.redoStack.push(operation);

    // 执行撤销函数
    try {
      if (operation.undo) {
        await operation.undo();
      }
      
      console.log(`已撤销模块 ${targetModuleId} 的操作: ${operation.type}`);
      console.log("aaaaaaaaaaaaa")
      console.log(moduleStack)
      console.log(operation)
      console.log(operation.filePath)
      console.log(operation.isOriginalImage)
      // 如果需要保存历史
  /*    if (operation.filePath && operation.isOriginalImage === false) {
        console.log("aaaaaaaaaaaaa")
        console.log("aaaaaaaaaaaaa")
        console.log("aaaaaaaaaaaaa")
        console.log("aaaaaaaaaaaaa")
        await this.saveOperationHistory(operation.filePath);
      }*/  await this.saveOperationHistory(operation.filePath);

      
      return operation;
    } catch (error) {
      console.error(`撤销模块 ${targetModuleId} 的操作失败: ${operation.type}`, error);
      return null;
    }
  }

  /**
   * 重做指定模块的上一个撤销的操作
   * @param {string|number} moduleId 模块ID（可选，默认为当前模块）
   * @returns {Promise<Object|null>} 重做的操作
   */
  async redo(moduleId = null) {
    const targetModuleId = moduleId || this.currentModuleId || 'default';
    
    if (!this.canRedo(targetModuleId)) return null;
    
    // 确保模块栈存在
    this._ensureModuleStacks(targetModuleId);
    
    // 从模块重做栈中取出操作
    const moduleRedoStack = this.moduleRedoStacks.get(targetModuleId);
    const operation = moduleRedoStack.pop();
    
    // 加入模块操作栈
    const moduleStack = this.moduleOperations.get(targetModuleId);
    moduleStack.push(operation);
    
    // 同时更新全局栈（向后兼容）
    this.operationStack.push(operation);
    this.redoStack = this.redoStack.filter(op => op !== operation);
    
    // 执行重做函数
    try {
      if (operation.redo) {
        await operation.redo();
      }
      
      console.log(`已重做模块 ${targetModuleId} 的操作: ${operation.type}`);
      
      // 如果需要保存历史
      if (operation.filePath && operation.isOriginalImage === false) {
        await this.saveOperationHistory(operation.filePath);
      }
      
      return operation;
    } catch (error) {
      console.error(`重做模块 ${targetModuleId} 的操作失败: ${operation.type}`, error);
      return null;
    }
  }

  /**
   * 指定模块是否可以撤销
   * @param {string|number} moduleId 模块ID（可选，默认为当前模块）
   * @returns {boolean}
   */
  canUndo(moduleId = null) {
    const targetModuleId = moduleId || this.currentModuleId || 'default';
    const moduleStack = this.moduleOperations.get(targetModuleId);
    return moduleStack && moduleStack.length > 0;
  }

  /**
   * 指定模块是否可以重做
   * @param {string|number} moduleId 模块ID（可选，默认为当前模块）
   * @returns {boolean}
   */
  canRedo(moduleId = null) {
    const targetModuleId = moduleId || this.currentModuleId || 'default';
    const moduleRedoStack = this.moduleRedoStacks.get(targetModuleId);
    return moduleRedoStack && moduleRedoStack.length > 0;
  }

  /**
   * 获取操作类型的中文名称
   * @param {string} type 操作类型
   * @returns {string} 中文名称
   */
  getOperationName(type) {
    const typeMap = {
      'crop': '裁剪',
      'adjustParams': '调整参数'
    };
    return typeMap[type] || type;
  }

  /**
   * 保存操作历史到文件
   * @param {string} filePath 图像文件路径
   * @returns {Promise<Object>} 保存结果
   */
  async saveOperationHistory(filePath) {
    if (!filePath || !window.electron) {
      console.log('不满足保存条件，跳过保存历史', {
        hasFile: !!filePath,
        hasElectron: !!window.electron
      });
      return { success: false, error: '不满足保存条件' };
    }
    
    try {
      // 检查是否有历史记录
      let hasHistory = false;
      for (const [moduleId, stack] of this.moduleOperations.entries()) {
        if (stack.length > 0) {
          hasHistory = true;
          break;
        }
      }
      
      if (!hasHistory) {
        console.log('所有模块的操作栈为空，无需保存');
        return { success: true, message: '操作栈为空，无需保存' };
      }
      
      console.log('准备保存操作历史:', {
        filePath: filePath,
        moduleCount: this.moduleOperations.size
      });
      
      // 构建可序列化的模块操作历史
      const serializableModuleOperations = {};
      for (const [moduleId, stack] of this.moduleOperations.entries()) {
        if (stack.length > 0) {
          serializableModuleOperations[moduleId] = stack.map(op => this._cleanOperationForSerialization(op));
        }
      }
      
      // 构建可序列化的模块重做栈
      const serializableModuleRedoStacks = {};
      for (const [moduleId, stack] of this.moduleRedoStacks.entries()) {
        if (stack.length > 0) {
          serializableModuleRedoStacks[moduleId] = stack.map(op => this._cleanOperationForSerialization(op));
        }
      }
      
      // 构建最终状态数据：每个模块的最新参数
      const moduleFinalStates = {};
      for (const [moduleId, stack] of this.moduleOperations.entries()) {
        if (stack.length > 0) {
          // 找到最后一个adjustParams操作
          const lastParamOp = [...stack].reverse().find(op => op.type === 'adjustParams');
          if (lastParamOp && lastParamOp.params) {
            moduleFinalStates[moduleId] = {
              params: JSON.parse(JSON.stringify(lastParamOp.params)),
              timestamp: lastParamOp.timestamp || Date.now()
            };
          }
        }
      }

      console.log(this.operationStack)
      console.log(this.operationStack)

      // 构建完整的数据对象
      const serializableData = {
        filePath: filePath,
        moduleOperations: serializableModuleOperations,
        moduleRedoStacks: serializableModuleRedoStacks,
        moduleFinalStates: moduleFinalStates,
        currentModuleId: this.currentModuleId,
        timestamp: Date.now(),
        
        // 兼容旧版本
        operationStack: this.operationStack.map(op => this._cleanOperationForSerialization(op)),
        redoStack: this.redoStack.map(op => this._cleanOperationForSerialization(op)),
        finalState: {
          params: this.getCurrentModuleParams(),
          timestamp: Date.now()
        }
      };
      
      // 确保数据完全可序列化
      const jsonString = JSON.stringify(serializableData);
      const finalData = JSON.parse(jsonString);
      
      console.log('IPC调用save-image-history前...');
      console.log(finalData);
      const result = await window.electron.invoke('save-image-history', finalData);
      console.log('IPC调用save-image-history后, 结果:', result);
      
      if (result.success) {
        console.log('操作历史已保存到:', result.historyFilePath);
      } else {
        console.error('保存操作历史失败:', result.error);
      }
      
      return result;
    } catch (error) {
      console.error('保存操作历史出错:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清理操作对象，移除函数，准备序列化
   * @param {Object} op 操作对象
   * @returns {Object} 清理后的操作对象
   * @private
   */
  _cleanOperationForSerialization(op) {
    return {
      type: op.type,
      filePath: op.filePath,
      isOriginalImage: op.isOriginalImage,
      params: op.params ? JSON.parse(JSON.stringify(op.params)) : {},
      timestamp: op.timestamp,
      moduleId: op.moduleId,
      prevParams: op.prevParams ? JSON.parse(JSON.stringify(op.prevParams)) : undefined
    };
  }

  /**
   * 加载历史操作
   * @param {string} filePath 图像文件路径
   * @param {Function} resetParamsCallback 重置参数的回调函数
   * @param {Function} updateParamsCallback 更新参数的回调函数
   * @returns {Promise<Object>} 加载结果
   */
  async loadOperationHistory(filePath, resetParamsCallback, updateParamsCallback) {
    if (!filePath || !window.electron) {
      console.log('未提供文件路径或electron不可用，无法加载历史');
      if (resetParamsCallback) resetParamsCallback();
      return { success: false, error: '未提供文件路径或electron不可用' };
    }
    
    try {
      console.log('尝试加载图像历史:', filePath);
      // 尝试加载历史文件
      const result = await window.electron.invoke('load-image-history', { filePath });
      console.log('加载历史结果:', result);
      
      if (result.success) {
        // 检查是否是新格式（有moduleOperations）
        if (result.data.moduleOperations) {
          // 使用新格式加载
          this._loadModuleHistoryData(result.data, updateParamsCallback);
        } else {
          // 兼容旧格式
          // 清空当前所有历史
          this.moduleOperations.clear();
          this.moduleRedoStacks.clear();
          
          // 有最终状态 - 优先级1：应用最终状态
          if (result.data.finalState && result.data.finalState.params) {
            console.log('发现最终状态，直接应用:', result.data.finalState);
            
            if (updateParamsCallback) {
              updateParamsCallback(result.data.finalState.params);
            }
          }
          
          // 重建操作栈 - 优先级2：重建操作栈以支持撤销/重做
          this.rebuildOperationStacks(result.data, updateParamsCallback);
        }
        
        return { 
          success: true, 
          isOriginalImage: this._hasNoOperations(),
          historyFilePath: result.historyFilePath
        };
      } else {
        console.log('无历史或加载历史失败:', result.message || '无历史');
        
        // 清空操作栈
        this.moduleOperations.clear();
        this.moduleRedoStacks.clear();
        this.operationStack = [];
        this.redoStack = [];
        
        // 重置参数
        if (resetParamsCallback) {
          resetParamsCallback();
        }
        
        return { 
          success: false, 
          message: result.message || '无历史',
          isOriginalImage: true
        };
      }
    } catch (error) {
      console.error('加载历史出错:', error);
      
      // 清空操作栈
      this.moduleOperations.clear();
      this.moduleRedoStacks.clear();
      this.operationStack = [];
      this.redoStack = [];
      
      // 重置参数
      if (resetParamsCallback) {
        resetParamsCallback();
      }
      
      return { success: false, error: error.message, isOriginalImage: true };
    }
  }

  /**
   * 判断是否没有任何操作历史
   * @returns {boolean} 如果没有任何操作历史，返回true
   * @private
   */
  _hasNoOperations() {
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      if (stack.length > 0) {
        return false;
      }
    }
    return true;
  }

  /**
   * 加载模块历史数据
   * @param {Object} data 历史数据
   * @param {Function} updateParamsCallback 更新参数回调
   * @private
   */
  _loadModuleHistoryData(data, updateParamsCallback) {
    // 清空当前所有历史
    this.moduleOperations.clear();
    this.moduleRedoStacks.clear();
    
    // 设置当前模块ID
    if (data.currentModuleId) {
      this.currentModuleId = data.currentModuleId;
    }
    
    // 加载模块操作历史
    if (data.moduleOperations) {
      for (const [moduleId, operations] of Object.entries(data.moduleOperations)) {
        if (Array.isArray(operations) && operations.length > 0) {
          this._ensureModuleStacks(moduleId);
          const moduleStack = this.moduleOperations.get(moduleId);
          
          // 重建操作对象，添加undo/redo函数
          operations.forEach(op => {
            moduleStack.push({
              ...op,
              // 添加撤销函数
              undo: function() {
                console.log(`模块${moduleId}的撤销操作被调用:`, op.type);
                
                if (op.type === 'adjustParams' && updateParamsCallback && op.prevParams) {
                  // 应用前一个状态的参数
                  updateParamsCallback(op.prevParams, moduleId);
                }
              },
              // 添加重做函数
              redo: function() {
                console.log(`模块${moduleId}的重做操作被调用:`, op.type);
                
                if (op.type === 'adjustParams' && updateParamsCallback && op.params) {
                  updateParamsCallback(op.params, moduleId);
                }
              }
            });
          });
        }
      }
    }
    
    // 加载模块重做栈
    if (data.moduleRedoStacks) {
      for (const [moduleId, operations] of Object.entries(data.moduleRedoStacks)) {
        if (Array.isArray(operations) && operations.length > 0) {
          this._ensureModuleStacks(moduleId);
          const moduleRedoStack = this.moduleRedoStacks.get(moduleId);
          
          // 重建操作对象，添加undo/redo函数
          operations.forEach(op => {
            moduleRedoStack.push({
              ...op,
              // 添加撤销函数
              undo: function() {
                console.log(`模块${moduleId}的撤销操作被调用:`, op.type);
                
                if (op.type === 'adjustParams' && updateParamsCallback && op.prevParams) {
                  updateParamsCallback(op.prevParams, moduleId);
                }
              },
              // 添加重做函数
              redo: function() {
                console.log(`模块${moduleId}的重做操作被调用:`, op.type);
                
                if (op.type === 'adjustParams' && updateParamsCallback && op.params) {
                  updateParamsCallback(op.params, moduleId);
                }
              }
            });
          });
        }
      }
    }
    
    // 应用模块最终状态
    if (data.moduleFinalStates && updateParamsCallback) {
      for (const [moduleId, state] of Object.entries(data.moduleFinalStates)) {
        if (state.params) {
          if (moduleId == this.currentModuleId) {
            // 当前模块直接应用
            updateParamsCallback(state.params, moduleId);
          }
        }
      }
    }
    
    // 重建全局操作栈（兼容性）
    this.operationStack = [];
    this.redoStack = [];
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      this.operationStack.push(...stack);
    }
    for (const [moduleId, stack] of this.moduleRedoStacks.entries()) {
      this.redoStack.push(...stack);
    }
    
    console.log('模块历史加载完成，当前模块:', this.currentModuleId);
  }

  /**
   * 获取当前模块的最新参数
   * @returns {Object} 当前模块的最新参数
   */
  getCurrentModuleParams() {
    if (!this.currentModuleId) return {};
    
    const moduleStack = this.moduleOperations.get(this.currentModuleId);
    if (!moduleStack || moduleStack.length === 0) return {};
    
    // 找到最后一个adjustParams操作
    const lastParamOp = [...moduleStack].reverse().find(op => op.type === 'adjustParams');
    return lastParamOp && lastParamOp.params ? { ...lastParamOp.params } : {};
  }

  /**
   * 获取指定模块的最新参数
   * @param {string|number} moduleId 模块ID
   * @returns {Object} 模块的最新参数
   */
  getModuleParams(moduleId) {
    if (!moduleId) return {};
    
    const moduleStack = this.moduleOperations.get(moduleId);
    if (!moduleStack || moduleStack.length === 0) return {};
    
    // 找到最后一个adjustParams操作
    const lastParamOp = [...moduleStack].reverse().find(op => op.type === 'adjustParams');
    return lastParamOp && lastParamOp.params ? { ...lastParamOp.params } : {};
  }

  /**
   * 获取所有模块的最新参数
   * @returns {Object} 所有模块的最新参数，格式为 {moduleId: paramsObject}
   */
  getAllModuleParams() {
    const allParams = {};
    
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      if (stack.length > 0) {
        // 找到最后一个adjustParams操作
        const lastParamOp = [...stack].reverse().find(op => op.type === 'adjustParams');
        if (lastParamOp && lastParamOp.params) {
          allParams[moduleId] = { ...lastParamOp.params };
        }
      }
    }
    
    return allParams;
  }

  /**
   * 获取除当前模块外的所有模块参数
   * @returns {Object} 除当前模块外的所有模块参数，格式为 {moduleId: paramsObject}
   */
  getOtherModuleParams() {
    const otherParams = {};
    
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      // 排除当前模块
      if (moduleId !== this.currentModuleId && stack.length > 0) {
        // 找到最后一个adjustParams操作
        const lastParamOp = [...stack].reverse().find(op => op.type === 'adjustParams');
        if (lastParamOp && lastParamOp.params) {
          otherParams[moduleId] = { ...lastParamOp.params };
        }
      }
    }
    
    return otherParams;
  }

  /**
   * 获取所有模块的操作计数
   * @returns {Object} 格式为 {moduleId: count}
   */
  getModuleOperationCounts() {
    const counts = {};
    
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      counts[moduleId] = stack.length;
    }
    
    return counts;
  }

  /**
   * 重建操作栈 (向后兼容旧版本)
   * @param {Object} data 历史数据
   * @param {Function} updateParamsCallback 更新参数回调
   */
  rebuildOperationStacks(data, updateParamsCallback) {
    // 清空当前操作栈
    this.operationStack = [];
    this.redoStack = [];
    
    if (!data) return;
    
    // 重建操作栈
    if (data.operationStack && Array.isArray(data.operationStack)) {
      this.rebuildOperations(data.operationStack, updateParamsCallback);
    }
    
    // 重建重做栈
    if (data.redoStack && Array.isArray(data.redoStack)) {
      // 重做栈不需要应用操作，只需要构建对象
      this.redoStack = data.redoStack.map(op => ({
        ...op,
        redo: function() {
          console.log('重建的重做操作被调用:', op.type);
          
          // 根据操作类型执行不同的重做逻辑
          if (op.type === 'adjustParams' && updateParamsCallback && op.params) {
            updateParamsCallback(op.params);
          }
        }
      }));
    }
    
    // 同时更新到模块化结构
    // 为每个操作分配默认模块ID
    const defaultModuleId = 'default';
    this._ensureModuleStacks(defaultModuleId);
    this.moduleOperations.set(defaultModuleId, [...this.operationStack]);
    this.moduleRedoStacks.set(defaultModuleId, [...this.redoStack]);
    
    console.log('重建后的操作栈:', this.operationStack.length, '重做栈:', this.redoStack.length);
  }

  /**
   * 重建操作 (向后兼容旧版本)
   * @param {Array} operations 操作数组
   * @param {Function} updateParamsCallback 更新参数回调
   */
  rebuildOperations(operations, updateParamsCallback) {
    if (!operations || !Array.isArray(operations)) return;
    
    // 首先按时间戳排序
    const sortedOperations = [...operations].sort((a, b) => a.timestamp - b.timestamp);
    
    // 然后重建操作
    this.operationStack = sortedOperations.map(op => {
      const newOp = {
        ...op,
        // 添加撤销函数
        undo: function() {
          console.log('重建的撤销操作被调用:', op.type);
          
          // 根据操作类型执行不同的撤销逻辑
          if (op.type === 'adjustParams' && updateParamsCallback && op.prevParams) {
            // 应用前一个状态的参数
            updateParamsCallback(op.prevParams);
          }
          // 可以添加其他类型操作的撤销逻辑
        },
        // 添加重做函数
        redo: function() {
          console.log('重建的重做操作被调用:', op.type);
          
          // 根据操作类型执行不同的重做逻辑
          if (op.type === 'adjustParams' && updateParamsCallback && op.params) {
            updateParamsCallback(op.params);
          }
          // 可以添加其他类型操作的重做逻辑
        }
      };
      
      return newOp;
    });
    
    // 应用所有操作
    this.applyOperations(this.operationStack, updateParamsCallback);
  }

  /**
   * 应用操作 (向后兼容旧版本)
   * @param {Array} operations 操作数组
   * @param {Function} updateParamsCallback 更新参数回调
   */
  applyOperations(operations, updateParamsCallback) {
    if (!operations || !Array.isArray(operations) || !updateParamsCallback) return;
    
    // 找到最后一个adjustParams操作
    const lastParamOp = [...operations].reverse().find(op => op.type === 'adjustParams');
    
    if (lastParamOp && lastParamOp.params) {
      // 应用最后一个参数调整
      updateParamsCallback(lastParamOp.params);
    }
  }

  /**
   * 获取最后一个操作 (向后兼容旧版本)
   * @returns {Object|null} 最后一个操作
   */
  getLastOperation() {
    if (this.operationStack.length === 0) {
      return null;
    }
    return this.operationStack[this.operationStack.length - 1];
  }

  /**
   * 获取可序列化的数据 (向后兼容旧版本)
   * @param {string} filePath 图像文件路径
   * @returns {Object} 可序列化的数据
   */
  getSerializableData(filePath) {
    // 使用新版本的方法，包含更完整的信息
    const serializableModuleOperations = {};
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      if (stack.length > 0) {
        serializableModuleOperations[moduleId] = stack.map(op => this._cleanOperationForSerialization(op));
      }
    }
    
    const serializableModuleRedoStacks = {};
    for (const [moduleId, stack] of this.moduleRedoStacks.entries()) {
      if (stack.length > 0) {
        serializableModuleRedoStacks[moduleId] = stack.map(op => this._cleanOperationForSerialization(op));
      }
    }
    
    const moduleFinalStates = {};
    for (const [moduleId, stack] of this.moduleOperations.entries()) {
      if (stack.length > 0) {
        const lastParamOp = [...stack].reverse().find(op => op.type === 'adjustParams');
        if (lastParamOp && lastParamOp.params) {
          moduleFinalStates[moduleId] = {
            params: JSON.parse(JSON.stringify(lastParamOp.params)),
            timestamp: lastParamOp.timestamp || Date.now()
          };
        }
      }
    }
    
    return {
      filePath: filePath,
      moduleOperations: serializableModuleOperations,
      moduleRedoStacks: serializableModuleRedoStacks,
      moduleFinalStates: moduleFinalStates,
      currentModuleId: this.currentModuleId,
      
      // 向后兼容
      operationStack: this.operationStack.map(op => this._cleanOperationForSerialization(op)),
      redoStack: this.redoStack.map(op => this._cleanOperationForSerialization(op)),
      finalState: {
        params: this.getCurrentModuleParams(),
        timestamp: Date.now()
      }
    };
  }
}

// 导出单例
// 在类定义后添加命名导出
// 同时保留默认导出
// const instance = new HistoryService();
export default reactive(new HistoryService());