<template>
  <div class="tool-panel-container">
    <div class="panel-header">
      <h3>工具面板</h3>
      <el-button type="link" @click="openSettings" class="settings-button">设置</el-button>
    </div>
    
    <!-- 工具区域 -->
    <el-collapse v-model="activePanels" accordion class="tool-collapse">
      <!-- 基础调整 -->
      <el-collapse-item title="基础调整" name="basic">
        <basic-adjustment 
          :params="editParams"
          :selectedFile="selectedImage"
          :currentBaselineImage="currentBaselineImage"
          @update-params="handleParamsUpdate"
          @update-image-url-display-base64="handleImageDisplayBase64Update"
          @update-last-task-id="updateLastTaskId"
          @update-apply-edit="handleApplyEditUpdate"
        />
      </el-collapse-item>
      
      <!-- 裁剪与旋转 -->
      <el-collapse-item title="裁剪与旋转" name="transform">
        <transform-tools 
          :params="{currentTool: currentTool}"
          @update-params="handleParamsUpdate"
          @set-tool="setCurrentTool"
          @rotate="handleRotate"
        />
      </el-collapse-item>
      
      <!-- 文档优化 -->
      <el-collapse-item title="文档优化" name="document">
        <document-tools 
          :params="editParams" 
          @update-params="handleParamsUpdate"
          @detect-boundaries="detectBoundaries"
          @perspective-correction="perspectiveCorrection"
        />
      </el-collapse-item>
    </el-collapse>
    
    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button type="success" @click="applyChanges" :loading="isProcessing">应用更改</el-button>
      <el-button @click="resetChanges" :disabled="isProcessing">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watchEffect, watch } from 'vue';
import { ElMessage } from 'element-plus';
import BasicAdjustment from './BasicAdjustment.vue';
import TransformTools from './TransformTools.vue';
import DocumentTools from './DocumentTools.vue';
import historyService from "@/services/history-service";

const props = defineProps({
  selectedImage: {
    type: String,
    default: ''
  },
  currentParams: {
    type: Object,
    default: () => ({})
  },
  currentBaselineImage: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update-params', 'apply-edit', 'set-tool', 'open-settings', 'update:currentBaselineImage']);

// 面板状态
const activePanels = ref(['basic']);
const currentTool = ref('');
const isProcessing = ref(false);
const lastTaskId = ref(''); // 存储最后一次处理的TaskID
const currentBaselineImage = ref(props.currentBaselineImage);

// 编辑参数
const editParams = reactive({
  brightness: 0,
  contrast: 0,
  saturation: 0,
  sharpness: 0,
  denoise: 0,
  rotation: 0,
  crop: null
});

// 添加监听：当从父组件接收到新参数时更新
watch(() => props.currentParams, (newParams) => {
/*  if (newParams && Object.keys(newParams).length > 0) {
    console.log('工具面板收到新参数:', newParams);

    // 直接应用新参数，不再先重置
    Object.keys(newParams).forEach(key => {
      if (key in editParams) {
        editParams[key] = newParams[key];
        console.log(`工具面板更新参数 ${key}: ${newParams[key]}`);
      }
    });

    console.log('工具面板最终参数:', {...editParams});
  }*/
}, { deep: true, immediate: true });

watch(() => props.selectedImage, (newFile) => {
  if (newFile) {
    currentBaselineImage.value=""
  }
});

// // 监听 currentBaselineImage 变化，向父组件同步
// watch(currentBaselineImage, (val) => {
//   emit('update:currentBaselineImage', val);
// });

// 计算属性
const hasSelectedImage = computed(() => !!props.selectedImage);

// 监听面板切换
watch(() => activePanels.value, async (newPanel, oldPanel) => {
  if (newPanel.length > 0 && oldPanel.length > 0 && newPanel[0] !== oldPanel[0]) {
    console.log(`工具面板切换: ${oldPanel[0]} -> ${newPanel[0]}`);
    
    // 只有当lastTaskId存在才创建基准图
    if (lastTaskId.value) {
      await createBaselineFromCurrentState(oldPanel[0], newPanel[0]);
    } else {
      console.log('没有可用的TaskID，跳过创建基准图');
    }
  }
}, { deep: true });

// 创建基准图
const createBaselineFromCurrentState = async (oldPanel, newPanel) => {
  if (!lastTaskId.value) {
    console.log('没有可用的TaskID，无法创建基准图');
    return;
  }
  
  try {
    console.log(`尝试从TaskID创建基准图: ${lastTaskId.value}`);
    isProcessing.value = true;
    
    // 生成会话ID (使用图像路径的哈希)
    const sessionId = generateSessionId(props.selectedImage);
    
    // 创建基准图
    const result = await window.electron.invoke('set-baseline-image', {
      sessionId: sessionId,
      taskId: lastTaskId.value
    });
    
    if (result.success) {
      console.log('基准图创建成功:', result);
      currentBaselineImage.value = result.resultData.baselineImagePath;
      console.log('currentBaselineImage 已赋值:', currentBaselineImage.value);
      emit('update:currentBaselineImage', currentBaselineImage.value);
      // 切换到新面板后重置参数
      resetParamsForPanel(newPanel);
      ElMessage.success(`已切换到${getPanelName(newPanel)}模式`);
    } else {
      console.error('创建基准图失败:', result.error);
      ElMessage.warning(`切换到${getPanelName(newPanel)}模式，但无法创建基准图`);
    }
  } catch (error) {
    console.error('设置基准图时出错:', error);
    ElMessage.error(`切换面板时出错: ${error.message}`);
  } finally {
    isProcessing.value = false;
  }
};

// 根据面板ID获取面板名称
const getPanelName = (panelId) => {
  const names = {
    'basic': '基础调整',
    'transform': '裁剪与旋转',
    'document': '文档优化'
  };
  return names[panelId] || panelId;
};

// 重置面板参数
const resetParamsForPanel = (panel) => {
  console.log(`重置面板[${panel}]的参数`);
  
  // 保存当前参数
  const savedParams = { ...editParams };
  
  // 重置所有参数
  Object.keys(editParams).forEach(key => {
    if (typeof editParams[key] === 'number') {
      editParams[key] = 0;
    } else if (editParams[key] === null || typeof editParams[key] === 'object') {
      editParams[key] = null;
    }
  });
  
  // 保留特定参数
  switch (panel) {
    case 'basic':
      // 基础调整不需要特殊处理
      break;
    case 'transform':
      // 裁剪参数保留上次设置
      editParams.crop = savedParams.crop;
      break;
    case 'document':
      // 文档优化特有参数保留
      break;
  }
  
  // 发送更新
  emit('update-params', { ...editParams });
};

// 生成会话ID
const generateSessionId = (imagePath) => {
  if (!imagePath) return 'default-session';
  
  // 使用简单的字符串哈希
  let hash = 0;
  for (let i = 0; i < imagePath.length; i++) {
    const char = imagePath.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return `session_${Math.abs(hash).toString(16)}`;
};

// 方法
const handleParamsUpdate = (params) => {
  Object.assign(editParams, params);
  // 使用解构创建新对象，避免引用传递
  emit('update-params', { ...editParams });
};

const handleApplyEditUpdate = () => {
  emit('apply-edit');
}

const handleImageDisplayBase64Update = (params) => {
  emit('apply-base64Url',params);
}

const setCurrentTool = (tool) => {
  currentTool.value = tool;
  emit('set-tool', tool);
};

const handleRotate = async (degrees) => {
  isProcessing.value = true;
  
  try {
    // 调用后端处理旋转
    const result = await window.electron.invoke('apply-image-edit', {
      filePath: props.selectedImage,
      operation: 'rotate',
      params: { angle: degrees }
    });
    
    if (result.success) {
      const imageData = await window.electron.invoke('get-cached-image', {
        taskId: result.resultData.taskId,
        quality: 90
      });

      console.log('模块处理成功:');
      console.log("paramsCopy",imageData);

      ElMessage.success('旋转操作已应用');
      handleImageDisplayBase64Update(`${imageData.imageData}`)
      //  emit('operation-applied'); // Notify parent (App.vue) to refresh display
    } else {
      ElMessage.error(result.error || '旋转操作失败');
    }
  } catch (error) {
    ElMessage.error('旋转操作发生错误');
    console.error('旋转错误:', error);
  } finally {
    isProcessing.value = false;
  }
};

const detectBoundaries = async () => {
  ElMessage.info('正在检测文档边界...');
  isProcessing.value = true;
  
  try {
    // 调用后端处理边界检测
    const result = await window.electron.invoke('apply-image-edit', {
      filePath: props.selectedImage,
      operation: 'detect-boundaries',
      params: {}
    });
    
    if (result.success) {
      ElMessage.success('边界检测已完成');
      emit('apply-edit');
    } else {
      ElMessage.error(result.error || '边界检测失败');
    }
  } catch (error) {
    ElMessage.error('边界检测发生错误');
    console.error('边界检测错误:', error);
  } finally {
    isProcessing.value = false;
  }
};

const perspectiveCorrection = async () => {
  ElMessage.info('正在应用透视校正...');
  isProcessing.value = true;
  
  try {
    // 调用后端处理透视校正
    const result = await window.electron.invoke('apply-image-edit', {
      filePath: props.selectedImage,
      operation: 'perspective-correction',
      params: {}
    });
    
    if (result.success) {
      // 成功静默执行
      emit('apply-edit');
    } else {
      ElMessage.error(result.error || '透视校正失败');
    }
  } catch (error) {
    ElMessage.error('透视校正发生错误');
    console.error('透视校正错误:', error);
  } finally {
    isProcessing.value = false;
  }
};

const applyChanges = async () => {
  if (!props.selectedImage) return;
  
  isProcessing.value = true;
  
  try {
    // 调用后端处理基础参数调整
    const result = await window.electron.invoke('apply-image-edit', {
      filePath: props.selectedImage,
      operation: 'adjust-params',
      params: editParams
    });
    
    if (result.success) {
      // 成功静默执行
      emit('apply-edit');
      // 处理成功后重置参数
      resetChanges();
    } else {
      ElMessage.error(result.error || '图像处理失败');
    }
  } catch (error) {
    ElMessage.error('图像处理发生错误');
    console.error('应用更改错误:', error);
  } finally {
    isProcessing.value = false;
  }
};

const resetChanges = () => {
  // 重置所有参数
  Object.keys(editParams).forEach(key => {
    if (typeof editParams[key] === 'number') {
      editParams[key] = 0;
    }
  });
  editParams.crop = null;
  emit('update-params', editParams);
};

// 打开设置
const openSettings = () => {
  emit('open-settings');
};

// 接收TaskID更新
const updateLastTaskId = (taskId) => {
  if (taskId) {
    console.log(`更新最后处理的TaskID: ${taskId}`);
    lastTaskId.value = taskId;
  }
};

// 暴露方法给父组件
defineExpose({
  updateLastTaskId
});
</script>

<style scoped>
.tool-panel-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 10px 15px;
  background-color: var(--bg-color-light);
  border-left: 1px solid var(--border-color);
}

.panel-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 0 10px;
  border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
  margin: 0;
  color: var(--text-color);
}

.settings-button {
  padding: 0;
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  background: none;
  border: none;
  text-decoration: none;
}

.settings-button:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* 工具折叠面板样式 */
.tool-collapse {
  flex: 1;
  overflow: auto;
  /* 视觉上隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
}

.tool-collapse::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* 移除el-collapse的顶部边框 */
:deep(.el-collapse) {
  border-top: none;
}

/* 底部操作按钮 */
.action-buttons {
  flex-shrink: 0;
  padding: 15px 0 5px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid var(--border-color);
}
</style> 