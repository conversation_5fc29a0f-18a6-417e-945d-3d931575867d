{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "allowJs": true, "checkJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/**/*.js", "src/**/*.vue", "src/**/*.jsx"], "exclude": ["node_modules"]}