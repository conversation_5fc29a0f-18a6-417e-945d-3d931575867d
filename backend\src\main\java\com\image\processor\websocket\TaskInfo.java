package com.image.processor.websocket;

import org.java_websocket.WebSocket;

/**
 * 任务信息类，存储任务状态
 */
public class TaskInfo {
    private final String id;
    private final WebSocket conn;
    private volatile boolean cancelled = false;
    
    public TaskInfo(String id, WebSocket conn) {
        this.id = id;
        this.conn = conn;
    }
    
    public WebSocket getConn() {
        return conn;
    }
    
    public String getId() {
        return id;
    }
    
    public void cancel() {
        this.cancelled = true;
    }
    
    public boolean isCancelled() {
        return cancelled;
    }
} 