/**
 * WebSocket服务
 * 负责管理与后端的WebSocket连接
 */

import WebSocket from 'ws';
import { WS_CONFIG } from '../config/app-config.js';
import { 
  restartBackendService, 
  incrementReconnectAttempts, 
  resetReconnectAttempts,
  BACKEND_CONFIG 
} from './backend-service.js';

// WebSocket状态
const wsState = {
  ws: null,
  currentPort: null,
  reconnectTimer: null,
  retryCount: 0,
  isConnecting: false
};

// 存储任务回调的Map
const taskCallbacks = new Map();

// 指示WebSocket是否已连接
let webSocketConnected = false;

// 主窗口引用，用于发送消息给渲染进程
let mainWindow = null;

/**
 * 设置主窗口引用
 * @param {Electron.BrowserWindow} window 主窗口对象
 */
export function setMainWindow(window) {
  mainWindow = window;
}

/**
 * 生成唯一的任务ID
 * @returns {string} 唯一任务ID
 */
export function generateTaskId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 设置WebSocket事件处理
 * @param {WebSocket} ws WebSocket实例
 */
function setupWebSocketHandlers(ws) {
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      
      if (message.type === 'task_update') {
        handleTaskUpdate(message);
      } else if (message.type === 'image_data') {
        handleImageData(message);
      } else if (message.type === 'error') {
        console.error('服务器错误:', message.message);
        // 查找对应的任务回调
        const taskId = message.taskId;
        if (taskId) {
          const callback = taskCallbacks.get(taskId);
          if (callback) {
            callback.reject(new Error(message.message));
            taskCallbacks.delete(taskId);
          }
        }
      } else if (message.type === 'result') {
        // 处理文件夹处理结果
        const taskId = message.taskId;
        if (taskId) {
          const callback = taskCallbacks.get(taskId);
          if (callback) {
            callback.resolve(message);
            taskCallbacks.delete(taskId);
          }
        }
      }
    } catch (error) {
      console.error('解析WebSocket消息时出错:', error);
    }
  });

  ws.on('close', () => {
    console.log('与图像处理服务的连接已断开，尝试重连...');
    wsState.ws = null;
    wsState.reconnectTimer = setTimeout(connectToWebSocket, WS_CONFIG.RECONNECT_INTERVAL);
  });

  ws.on('error', (error) => {
    console.error('WebSocket错误:', error);
  });
}

/**
 * 处理任务更新消息
 * @param {Object} message 任务更新消息
 */
function handleTaskUpdate(message) {
  const { taskId, status, progress, data } = message;

  const taskCallback = taskCallbacks.get(taskId);
  if (!taskCallback) {
      console.warn(`收到未知任务ID ${taskId} 的更新`);
      return; 
  }

  switch (status) {
    case 'progress':
      // 检查是否有 onProgress 回调
      if (typeof taskCallback.onProgress === 'function') {
           taskCallback.onProgress(progress, data);
      }
      break;
    case 'completed':
      // 检查是否有 resolve 回调
      if (typeof taskCallback.resolve === 'function') {
          taskCallback.resolve(data); // 调用存储的回调
      } else {
          console.warn(`任务 ${taskId} 缺少 resolve 回调`);
      }
      break;
    case 'failed':
    case 'error':
      // 检查是否有 reject 回调
      if (typeof taskCallback.reject === 'function') {
          taskCallback.reject(new Error(typeof data === 'string' ? data : JSON.stringify(data)));
      } else {
           console.warn(`任务 ${taskId} 缺少 reject 回调`);
      }
      break;
    case 'cancelled':
       // 检查是否有 reject 回调
       if (typeof taskCallback.reject === 'function') {
           taskCallback.reject(new Error('任务已取消'));
       } else {
           console.warn(`任务 ${taskId} 缺少 reject 回调 (用于取消)`);
       }
      break;
    default:
        console.warn(`收到未知的任务状态: ${status} for task ${taskId}`);
        break;
  }
}

/**
 * 处理图像数据消息
 * @param {Object} message 图像数据消息
 */
function handleImageData(message) {
  const { taskId, data } = message;
  console.log(`[WebSocket] Received image data for task ${taskId}, size: ${data.length / 1024}KB`);
  
  // 查找任务回调并传递图像数据
  const callback = taskCallbacks.get(taskId);
  if (callback) {
    // 检查callback类型，支持函数和对象两种情况
    if (typeof callback === 'function') {
      // 直接函数调用方式（get-cached-image使用的方式）
      callback({
        status: 'image_data',
        data: {
          format: message.format,
          imageData: data
        }
      });
    } else if (callback.callback && typeof callback.callback === 'function') {
      // 对象中包含callback函数的方式
      callback.callback({
        status: 'image_data',
        data: {
          format: message.format,
          imageData: data
        }
      });
    } else {
      // 旧的方式，为了兼容性保留
      callback({
        type: 'task_update',
        taskId: taskId,
        status: 'image_data',
        data: {
          format: message.format,
          imageData: data
        }
      });
    }
  } else {
    console.log(`[WebSocket] No callback found for image data task ${taskId}`);
  }
}

/**
 * 连接到WebSocket服务
 * @returns {Promise<boolean>} 连接是否成功的Promise
 */
export async function connectToWebSocket() {
  if (wsState.isConnecting) return false;
  
  wsState.isConnecting = true;
  webSocketConnected = false;
  
  console.log('开始连接WebSocket服务...');
  let ws; // 声明WebSocket变量
  
  for (const port of WS_CONFIG.PORTS) {
    try {
      console.log(`尝试连接到端口 ${port}...`);
      ws = new WebSocket(`ws://localhost:${port}/ws`);
      
      // 设置超时
      const connectionPromise = new Promise((resolve, reject) => {
        ws.on('open', () => {
          console.log(`成功连接到端口 ${port}`);
          webSocketConnected = true;
          wsState.ws = ws; // 保存WebSocket实例到状态中
          wsState.isConnecting = false;
          
          // 重置重连尝试计数
          resetReconnectAttempts();
          
          setupWebSocketHandlers(ws);
          resolve(true);
        });
        
        ws.on('error', (error) => {
          console.log(`连接端口 ${port} 失败:`, error);
          reject(error);
        });
        
        setTimeout(() => {
          reject(new Error('连接超时'));
        }, WS_CONFIG.CONNECT_TIMEOUT);
      });
      
      await connectionPromise;
      return true; // 成功连接，返回真
      
    } catch (error) {
      // 连接失败，继续下一个端口
      continue;
    }
  }
  
  // 所有端口都连接失败
  console.log('所有端口连接失败，等待重试...');
  wsState.isConnecting = false;
  
  // 检查是否需要重启后端服务
  const shouldRestart = incrementReconnectAttempts();
  
  if (shouldRestart) {
    console.log(`连接尝试多次后仍然失败，尝试重启后端服务`);
    restartBackendService();
    
    // 延迟后再次尝试连接
    setTimeout(connectToWebSocket, BACKEND_CONFIG.RESTART_DELAY);
  } else {
    // 短时间后重试连接
    setTimeout(connectToWebSocket, 1000);
  }
  
  return false;
}

/**
 * 发送WebSocket消息
 * @param {Object} message 要发送的消息对象
 * @returns {boolean} 是否发送成功
 */
export function sendWebSocketMessage(message) {
  if (!wsState.ws || wsState.ws.readyState !== WebSocket.OPEN) {
    console.error('WebSocket未连接，无法发送消息');
    return false;
  }
  
  try {
    wsState.ws.send(JSON.stringify(message));
    return true;
  } catch (error) {
    console.error('发送WebSocket消息失败:', error);
    return false;
  }
}

/**
 * 获取WebSocket连接状态
 * @returns {boolean} 是否已连接
 */
export function isConnected() {
  return webSocketConnected && wsState.ws && wsState.ws.readyState === WebSocket.OPEN;
}

/**
 * 设置任务回调
 * @param {string} taskId 任务ID
 * @param {Object|Function} callback 回调对象或函数
 */
export function setTaskCallback(taskId, callback) {
  taskCallbacks.set(taskId, callback);
}

/**
 * 移除任务回调
 * @param {string} taskId 任务ID
 * @returns {boolean} 是否成功移除
 */
export function removeTaskCallback(taskId) {
  return taskCallbacks.delete(taskId);
}

/**
 * 获取任务回调
 * @param {string} taskId 任务ID
 * @returns {Object|Function|undefined} 任务回调
 */
export function getTaskCallback(taskId) {
  return taskCallbacks.get(taskId);
}

export { wsState }; 