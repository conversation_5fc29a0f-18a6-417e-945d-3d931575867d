import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import App from './App.vue';
import router from './router';


// 创建Pinia状态管理实例
const pinia = createPinia();

// 创建应用实例
const app = createApp(App);

// 使用插件
app.use(pinia);
app.use(router);
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
});

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('应用错误:', err);
  console.log('错误信息:', info);
};



// 挂载应用
app.mount('#app'); 