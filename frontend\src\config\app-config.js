/**
 * 应用配置
 * 包含应用的默认设置和常量配置
 */

import { app } from 'electron';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// 定义获取用户数据路径的函数
function getUserDataPath() {
  if (app) {
    try {
      return app.getPath('userData');
    } catch (e) {
      return path.resolve('./userData');
    }
  }
  return path.resolve('./userData');
}

// 定义默认设置
export const defaultSettings = {
  basic: {
    // 使用函数获取路径，而不是直接调用app.getPath
    get thumbnailPath() {
      return path.join(getUserDataPath(), 'thumbnails');
    },
    outputMode: 'new', // 默认模式
    outputPattern: '{name}_processed',
    defaultFormat: 'jpg',
    outputFolderPath: '' // 默认输出文件夹路径为空
  },
  performance: {
    gpuAcceleration: true,
    cacheSize: 1000,
    threadCount: 4
  }
};

// WebSocket配置
export const WS_CONFIG = {
  PORTS: [11000, 21001, 31002],
  RECONNECT_INTERVAL: 5000, // 5秒重连间隔
  CONNECT_TIMEOUT: 5000,    // 5秒连接超时
  MAX_RETRIES: 3,           // 每个端口最大重试次数
  DEV_MODE: true  // 强制设置为开发模式
};

// 后端服务配置
export const BACKEND_CONFIG = {
  AUTO_START: false, // 直接设置为false，不自动启动后端
  RESTART_DELAY: 3000, // 重启延迟时间
  MAX_RECONNECT_ATTEMPTS: 5, // 最大重连次数
  JVM_OPTS: ['-Xmx2G'] // JVM参数
}; 