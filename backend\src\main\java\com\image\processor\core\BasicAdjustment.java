package com.image.processor.core;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;

import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import static org.bytedeco.opencv.global.opencv_core.addWeighted;
import static org.bytedeco.opencv.global.opencv_core.merge;
import static org.bytedeco.opencv.global.opencv_core.split;
import static org.bytedeco.opencv.global.opencv_core.subtract;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.MatVector;
import org.bytedeco.opencv.opencv_core.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 提供基本图像调整功能的类
 */
public class BasicAdjustment {
    private static final Logger logger = LoggerFactory.getLogger(BasicAdjustment.class);

    /**
     * 调整亮度和对比度（原地操作）
     * 
     * @param image 输入图像
     * @param brightness 亮度调整值，范围建议 [-100, 100]
     * @param contrast 对比度调整值，范围建议 [0, 3.0]，1.0表示不变
     * @return 成功返回true，失败返回false
     */
    public static boolean adjustBrightnessContrast(Mat image, double brightness, double contrast) {
        if (image == null || image.empty()) {
            logger.error("调整亮度对比度失败：无效的输入图像");
            return false;
        }
        
        try {
            // 归一化亮度参数，将[-100, 100]映射到[-255, 255]
            int normalizedBrightness = (int) (brightness * 2.55);
            
            // 使用OpenCV的公式: output = contrast * input + brightness
            // 直接修改原图像，不创建新的Mat对象
            image.convertTo(image, image.type(), contrast, normalizedBrightness);
            return true;
        } catch (Exception e) {
            logger.error("调整亮度对比度时出错", e);
            return false;
        }
    }
    
    /**
     * 调整饱和度（原地操作）
     * 
     * @param image 输入图像
     * @param saturation 饱和度调整值，范围建议 [0, 2]，1.0表示不变
     * @return 成功返回true，失败返回false
     */
    public static boolean adjustSaturation(Mat image, double saturation) {
        if (image == null || image.empty()) {
            logger.error("调整饱和度失败：无效的输入图像");
            return false;
        }
        
        // 如果饱和度接近1.0（默认值），则不做处理
        if (Math.abs(saturation - 1.0) < 0.01) {
            return true;
        }
        
        Mat hsvImage = null;
        MatVector hsvChannels = null;
        
        try {
            // 创建临时Mat对象
            hsvImage = new Mat();
            hsvChannels = new MatVector(3);
            
            // 转换到HSV色彩空间
            opencv_imgproc.cvtColor(image, hsvImage, opencv_imgproc.COLOR_BGR2HSV);
            
            // 分离通道
            split(hsvImage, hsvChannels);
            
            // 调整饱和度通道（索引1）
            Mat satChannel = hsvChannels.get(1);
            satChannel.convertTo(satChannel, satChannel.type(), saturation, 0);
            
            // 合并通道
            merge(hsvChannels, hsvImage);
            
            // 转换回BGR色彩空间
            opencv_imgproc.cvtColor(hsvImage, image, opencv_imgproc.COLOR_HSV2BGR);
            
            return true;
        } catch (Exception e) {
            logger.error("调整饱和度时出错", e);
            return false;
        } finally {
            // 主动释放资源
            if (hsvChannels != null) {
                for (long i = 0; i < hsvChannels.size(); i++) {
                    Mat channel = hsvChannels.get(i);
                    if (channel != null && !channel.isNull()) {
                        channel.release();
                    }
                }
                hsvChannels.close();
            }
            if (hsvImage != null && !hsvImage.isNull()) {
                hsvImage.release();
            }
        }
    }
    
    /**
     * 应用锐化效果（原地操作）
     * 
     * @param image 输入图像
     * @param amount 锐化程度，范围建议 [0, 5]，0表示不锐化
     * @return 成功返回true，失败返回false
     */
    public static boolean applySharpening(Mat image, double amount) {
        if (image == null || image.empty()) {
            logger.error("应用锐化失败：无效的输入图像");
            return false;
        }
        
        // 锐化程度为0，不做处理
        if (amount <= 0) {
            return true;
        }
        
        Mat blurred = null;
        Mat edges = null;
        
        try {
            // 创建临时Mat对象
            blurred = new Mat();
            edges = new Mat();
            
            // 使用高斯模糊
            Size ksize = new Size(0, 0); // 0表示自动根据sigma计算大小
            opencv_imgproc.GaussianBlur(image, blurred, ksize, 3.0, 3.0, 0);
            
            // 原图 - 模糊图 = 边缘图
            subtract(image, blurred, edges);
            
            // 原图 + amount * 边缘图 = 锐化图
            addWeighted(image, 1.0, edges, amount, 0.0, image);
            
            return true;
        } catch (Exception e) {
            logger.error("应用锐化效果时出错", e);
            return false;
        } finally {
            // 主动释放资源
            if (blurred != null && !blurred.isNull()) {
                blurred.release();
            }
            if (edges != null && !edges.isNull()) {
                edges.release();
            }
        }
    }
    
    /**
     * 将所有基本调整应用到图像（原地操作）
     * 
     * @param image 输入图像
     * @param brightness 亮度
     * @param contrast 对比度
     * @param saturation 饱和度
     * @param sharpness 锐度
     * @return 成功返回true，失败返回false
     */
    public static boolean applyAllAdjustments(Mat image, double brightness, double contrast, 
                                             double saturation, double sharpness) {
        if (image == null || image.empty()) {
            logger.error("应用所有调整失败：无效的输入图像");
            return false;
        }
        
        try {
            logger.debug("开始应用图像调整: 亮度={}, 对比度={}, 饱和度={}, 锐度={}",
                         brightness, contrast, saturation, sharpness);
            
            // 亮度对比度调整
            if (brightness != 0 || Math.abs(contrast - 1.0) >= 0.01) {
                logger.debug("正在调整亮度和对比度...");
                boolean success = adjustBrightnessContrast(image, brightness, contrast);
                if (!success) {
                    logger.error("亮度对比度调整失败");
                    return false;
                }
            }
            
            // 饱和度调整
            if (Math.abs(saturation - 1.0) >= 0.01) {
                logger.debug("正在调整饱和度...");
                boolean success = adjustSaturation(image, saturation);
                if (!success) {
                    logger.error("饱和度调整失败");
                    return false;
                }
            }
            
            // 锐化
            if (sharpness > 0) {
                logger.debug("正在应用锐化效果...");
                boolean success = applySharpening(image, sharpness);
                if (!success) {
                    logger.error("锐化处理失败");
                    return false;
                }
            }
            
            logger.debug("所有图像调整已成功应用");
            return true;
        } catch (Exception e) {
            logger.error("应用所有调整时出错", e);
            return false;
        }
    }
    
    /**
     * 应用基本调整并返回新图像（便于与旧代码兼容）
     * 
     * @param imagePath 输入图像路径
     * @param outputPath 输出图像路径
     * @param brightness 亮度
     * @param contrast 对比度
     * @param saturation 饱和度
     * @param sharpness 锐度
     * @return 成功返回true，失败返回false
     */
    public static boolean applyAdjustments(String imagePath, String outputPath, 
                                          double brightness, double contrast,
                                          double saturation, double sharpness) {
        logger.debug("正在处理图像: {} -> {}", imagePath, outputPath);
        
        Mat image = null;
        try {
            // 读取图像
            logger.debug("正在读取图像文件: {}", imagePath);
            image = loadImage(imagePath);
            
            if (image == null || image.empty()) {
                logger.error("无法读取图像文件: {}", imagePath);
                return false;
            }
            
            // 应用所有调整
            boolean success = applyAllAdjustments(image, brightness, contrast, saturation, sharpness);
            if (!success) {
                logger.error("应用图像调整失败");
                return false;
            }
            
            // 确保输出目录存在
            File outputFile = new File(outputPath);
            if (!outputFile.getParentFile().exists()) {
                outputFile.getParentFile().mkdirs();
            }
            
            // 保存图像
            logger.debug("正在保存处理后的图像: {}", outputPath);
            boolean saved = imwrite(outputPath, image);
            if (!saved) {
                logger.error("保存图像失败: {}", outputPath);
                return false;
            }
            
            logger.debug("图像处理完成: {}", outputPath);
            return true;
        } catch (Exception e) {
            logger.error("处理图像时出错: {} -> {}", imagePath, outputPath, e);
            return false;
        } finally {
            // 释放资源
            if (image != null && !image.isNull()) {
                image.release();
            }
        }
    }
    
    /**
     * 应用基本调整并返回新图像（便于与旧代码兼容）
     * 
     * @param image 输入图像
     * @param brightness 亮度
     * @param contrast 对比度
     * @param saturation 饱和度
     * @param sharpness 锐度
     * @return 处理后的新图像，失败返回null
     */
    public static Mat processImage(Mat image, double brightness, double contrast, 
                                  double saturation, double sharpness) {
        if (image == null || image.empty()) {
            logger.error("处理图像失败：无效的输入图像");
            return null;
        }
        
        try {
            // 创建输入图像的副本
            Mat result = image.clone();
            
            // 应用所有调整
            boolean success = applyAllAdjustments(result, brightness, contrast, saturation, sharpness);
            
            if (!success) {
                result.release();
                return null;
            }
            
            return result;
        } catch (Exception e) {
            logger.error("处理图像时出错", e);
            return null;
        }
    }
    
    /**
     * 从文件路径加载图像到Mat（使用ImageIO和JavaCV转换）
     *
     * @param imagePath 图像文件路径
     * @return 加载的Mat对象，失败返回null
     */
    public static Mat loadImage(String imagePath) {
        logger.debug("正在加载图像: {}", imagePath);
        
        Frame frame = null;
        OpenCVFrameConverter.ToMat converter = null;
        Java2DFrameConverter java2dConverter = null;
        
        try {
            File inputFile = new File(imagePath);
            if (!inputFile.exists() || !inputFile.canRead()) {
                logger.error("无法读取图像文件: {}", imagePath);
                return null;
            }
            
            // 使用ImageIO读取图像，避免中文路径问题
            BufferedImage bufferedImage = ImageIO.read(inputFile);
            if (bufferedImage == null) {
                logger.error("读取图像失败: {}", imagePath);
                return null;
            }
            
            // 转换BufferedImage到Mat
            converter = new OpenCVFrameConverter.ToMat();
            java2dConverter = new Java2DFrameConverter();
            frame = java2dConverter.convert(bufferedImage);
            Mat mat = converter.convert(frame);
            
            if (mat == null || mat.empty()) {
                logger.error("图像转换失败: {}", imagePath);
                return null;
            }
            
            logger.debug("图像加载成功: {}x{}, 通道数: {}", mat.cols(), mat.rows(), mat.channels());
            return mat;
        } catch (IOException e) {
            logger.error("加载图像文件出错: {}", imagePath, e);
            return null;
        } catch (Exception e) {
            logger.error("处理图像文件时发生未知错误: {}", imagePath, e);
            return null;
        } finally {
            if (frame != null) {
                frame.close();
            }
        }
    }
    
    /**
     * 将Mat保存为图像文件（可以指定格式和质量参数）
     *
     * @param mat 要保存的Mat对象
     * @param outputPath 输出文件路径
     * @param format 图像格式，如"jpg"、"png"等，默认根据路径扩展名
     * @param quality 质量参数（0-100，仅对JPEG有效）
     * @return 保存成功返回true，失败返回false
     */
    public static boolean saveImage(Mat mat, String outputPath, String format, int quality) {
        if (mat == null || mat.empty()) {
            logger.error("保存图像失败：无效的输入图像");
            return false;
        }
        
        if (format == null || format.trim().isEmpty()) {
            // 从路径获取格式
            int lastDot = outputPath.lastIndexOf('.');
            if (lastDot > 0 && lastDot < outputPath.length() - 1) {
                format = outputPath.substring(lastDot + 1).toLowerCase();
            } else {
                format = "jpg"; // 默认格式
            }
        }
        
        // 确保目录存在
        File outputFile = new File(outputPath);
        if (!outputFile.getParentFile().exists()) {
            outputFile.getParentFile().mkdirs();
        }
        
        try {
            if ("jpg".equals(format) || "jpeg".equals(format)) {
                // 对JPEG使用质量参数，直接传递整数数组
                int[] params = new int[] { opencv_imgcodecs.IMWRITE_JPEG_QUALITY, quality };
                return opencv_imgcodecs.imwrite(outputPath, mat, params);
            } else {
                // 其他格式使用默认参数
                return imwrite(outputPath, mat);
            }
        } catch (Exception e) {
            logger.error("保存图像时出错: {}", outputPath, e);
            return false;
        }
    }
    
    // imwrite辅助方法，直接调用OpenCV的imwrite，但添加了额外的日志记录
    private static boolean imwrite(String path, Mat img) {
        try {
            return opencv_imgcodecs.imwrite(path, img);
        } catch (Exception e) {
            logger.error("保存图像失败: {}", path, e);
            return false;
        }
    }
    
    // 修改imwrite辅助方法，使用int[]而不是IntPointer
    private static boolean imwrite(String path, Mat img, int[] params) {
        try {
            return opencv_imgcodecs.imwrite(path, img, params);
        } catch (Exception e) {
            logger.error("保存图像失败: {}", path, e);
            return false;
        }
    }
    
    /**
     * 图像基本调整参数类
     */
    public static class AdjustmentParams implements ModuleParams {
        private final double brightness;
        private final double contrast;
        private final double saturation;
        private final double sharpness;
        
        public AdjustmentParams(int brightness, int contrast, int saturation, int sharpness) {
            // 亮度：-100到100的整数转换为实际值
            this.brightness = brightness;
            
            // 对比度：-100到100的整数转换为0.1到3.0的浮点数
            this.contrast = (contrast == 0) ? 1.0 : (1.0 + (contrast / 100.0 * 2.0));
            
            // 饱和度：-100到100的整数转换为0.0到2.0的浮点数
            this.saturation = (saturation == 0) ? 1.0 : (1.0 + (saturation / 100.0));
            
            // 锐度：0到100的整数转换为0到3.0的浮点数
            this.sharpness = sharpness / 33.3;
        }
        
        public double getBrightness() {
            return brightness;
        }
        
        public double getContrast() {
            return contrast;
        }
        
        public double getSaturation() {
            return saturation;
        }
        
        public double getSharpness() {
            return sharpness;
        }
        
        @Override
        public String toString() {
            return String.format("AdjustmentParams{brightness=%.1f, contrast=%.2f, saturation=%.2f, sharpness=%.2f}", 
                                brightness, contrast, saturation, sharpness);
        }
        
        @Override
        public boolean isEmpty() {
            return brightness == 0 && 
                   Math.abs(contrast - 1.0) < 0.01 && 
                   Math.abs(saturation - 1.0) < 0.01 && 
                   sharpness < 0.01;
        }
        
        @Override
        public String getParamType() {
            return "basic_adjustment";
        }
        
        @Override
        public String toJsonString() {
            return String.format("{\"type\":\"%s\",\"brightness\":%.2f,\"contrast\":%.2f,\"saturation\":%.2f,\"sharpness\":%.2f}",
                               getParamType(), brightness, contrast, saturation, sharpness);
        }
        
        @Override
        public boolean fromJsonString(String jsonString) {
            try {
                // 简单解析JSON，实际应用中可以使用更完善的JSON库
                if (jsonString == null || !jsonString.contains(getParamType())) {
                    return false;
                }
                
                // 这里简化处理，实际应用中需要更完善的解析逻辑
                return true;
            } catch (Exception e) {
                return false;
            }
        }
    }
}