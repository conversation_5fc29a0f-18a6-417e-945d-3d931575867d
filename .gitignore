# Node.js dependencies
node_modules/
**/node_modules/
frontend/node_modules/
backend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
yarn.lock
.pnp.*

# Vue.js build artifacts
dist/
**/dist/
frontend/dist/
dist-electron/
**/dist-electron/
.vite/
**/.vite/

# Electron build artifacts
build/
**/build/
release/
**/release/
out/
**/out/
*.js.map
frontend/.cache/

# 编译后的JavaScript文件(从TypeScript编译生成的)
**/*.js.map
**/*.d.ts.map
**/*.tsbuildinfo
.tsc_cache
*.tgz

# Logs
logs/
**/logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Cache
.cache/
**/.cache/
.npm/
.eslintcache
.stylelintcache
.temp/
**/.temp/
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.history/

# TypeScript
*.tsbuildinfo
**/*.tsbuildinfo

# Testing
coverage/
**/coverage/
.nyc_output/

# Temporary files
.tmp/
**/tmp/
temp/
**/temp/
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# JavaCV binaries and libraries
backend/lib/
backend/native/
backend/bin/
**/jniLibs/
**/*.dll
**/*.so
**/*.dylib

# Generated folders
**/generated/
frontend/auto-imports.d.ts
frontend/components.d.ts

# Electron 应用打包输出
frontend/dist/
frontend/dist_electron/
frontend/electron-builder-output/
**/electron-builder-output/
**/.electron-vue/

# macOS specific files
.AppleDouble
.LSOverride
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows specific files
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk 
rules.md
