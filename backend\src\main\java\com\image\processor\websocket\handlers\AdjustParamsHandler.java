package com.image.processor.websocket.handlers;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import com.image.processor.utils.TaskIdUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.BasicAdjustment;
import com.image.processor.core.ImageProcessorService;
import com.image.processor.core.ImageProcessorService.ProcessingResult;
import com.image.processor.core.ProgressCallback;
import com.image.processor.websocket.TaskInfo;

import javax.imageio.ImageIO;

/**
 * 调整参数请求处理器
 */
public class AdjustParamsHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public AdjustParamsHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "adjust_params";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        // 提取参数
        final String taskId = request.getString("taskId");//前端传入的taskId,并且只能是原图路径生成
        final String inputPathWithFile = request.getString("inputPath");  // 缩略图路径，用于实际处理
      //  final String inputPath = TaskIdUtils.normalizePathAndSuffix(inputPathWithFile);

        final String inputPath;
        if (request.has("currentBaselineImage") && !request.isNull("currentBaselineImage")&& !request.getString("currentBaselineImage").equals("")) {
            inputPath = request.getString("currentBaselineImage");
        } else {
            inputPath = TaskIdUtils.normalizePathAndSuffix(inputPathWithFile);
        }

        // 获取原图路径（如果提供）
        final String originalPath;
        if (request.has("originalPath") && !request.isNull("originalPath")) {
            originalPath = request.getString("originalPath");
        } else {
            originalPath = null;
        }
        
        // 获取基准图任务ID（如果提供）
        final String baselineTaskId;
        if (request.has("baselineTaskId") && !request.isNull("baselineTaskId")) {
            baselineTaskId = request.getString("baselineTaskId");
        } else {
            baselineTaskId = null;
        }
        
        // 检查是否是全参数请求（用于创建基准图）
        final boolean isFullParams;
        if (request.has("isFullParams") && !request.isNull("isFullParams")) {
            isFullParams = request.getBoolean("isFullParams");
        } else {
            isFullParams = false;
        }
        
        final JSONObject paramsJson = request.getJSONObject("params");
        
        // 输出路径可选，如果没有提供则创建临时文件
        final String outputPathTmp;

        if (request.has("outputPath") && !request.isNull("outputPath")) {
            outputPathTmp = request.getString("outputPath");
        } else {
            outputPathTmp = null;
        }
        final String outputPath = TaskIdUtils.normalizePathAndSuffix(outputPathTmp);
        logger.info("收到图像调整参数请求: taskId={}, input={}, original={}, 基准图任务ID={}, 全参数请求={}, 参数={}", 
                   taskId, inputPath, originalPath, baselineTaskId, isFullParams, paramsJson.toString());
        
        // 发送任务接收确认
        sendTaskUpdate(conn, taskId, "accepted", 0, "任务已接受，准备处理");
        
        // 创建并存储任务信息
        final TaskInfo taskInfo = new TaskInfo(taskId, conn);
        activeTasks.put(taskId, taskInfo);
        
        // 提交任务到线程池
        taskExecutor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    // 如果任务已被取消，直接返回
                    if (taskInfo.isCancelled()) {
                        logger.info("任务已被取消，不执行: {}", taskId);
                        return;
                    }
                    
                    // 任务开始
                    sendTaskUpdate(conn, taskId, "started", 0, "开始处理图像调整");
                    
                    // 获取图像处理服务实例
                    ImageProcessorService processor = ImageProcessorService.getInstance();


                    // 从JSON解析参数
                    final BasicAdjustment.AdjustmentParams params = 
                        new BasicAdjustment.AdjustmentParams(
                            paramsJson.optInt("brightness", 0),
                            paramsJson.optInt("contrast", 0),
                            paramsJson.optInt("saturation", 0),
                            paramsJson.optInt("sharpness", 0)
                        );
                    // 创建进度回调
                    final ProgressCallback progressCallback = new ProgressCallback() {
                        @Override
                        public boolean onProgress(int progress, String message) {
                            if (taskInfo.isCancelled()) {
                                return false; // 返回false表示任务已取消，应停止处理
                            }
                            sendTaskUpdate(conn, taskId, "progress", progress, message);
                            return true; // 返回true表示继续处理
                        }
                    };
                    
                    // 使用新的处理服务应用图像调整（基于缓存）
                    ProcessingResult result;
                    
                    if (baselineTaskId != null) {
                        // 使用基准图流程
                        result = processor.applyAdjustmentsWithBaseline(
                            baselineTaskId,
                            inputPath,
                            params,
                            progressCallback
                        );
                    } else if (originalPath != null) {
                        // 使用原图路径生成TaskID，但实际处理缩略图
                        // 重要: 这是推荐的方式，确保使用原图路径生成taskId
                        result = processor.applyBasicAdjustmentsWithOriginal(
                            inputPath,           // 缩略图路径（用于处理）
                            originalPath,        // 原图路径（用于生成TaskID）
                            params, 
                            progressCallback
                        );
                    } else {
                        // 错误：缺少原图路径，无法正确生成taskId
                        logger.error("缺少原图路径，无法正确生成taskId: {}", inputPath);
                        sendTaskUpdate(conn, taskId, "error", 0, "配置错误: 缺少原图路径，无法正确生成taskId");
                        return;
                    }

                    // 任务已被取消
                    if (taskInfo.isCancelled()) {
                        logger.info("任务在处理过程中被取消: {}", taskId);
                        sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
                        // 释放图像资源
                        if (result != null) {
                            result.release();
                        }
                        return;
                    }
                    
                    // 根据结果发送消息
                    if (result != null) {
                        String storedTaskId = result.taskId;
                        
                        // 如果是全参数请求（用于创建基准图），则自动保存为基准图
                        String baselinePath = null;
                        if (isFullParams) {
                            try {
                                logger.info("全参数请求，自动保存为基准图: {}", storedTaskId);
                                baselinePath = processor.saveAsBaseline(storedTaskId);
                                if (baselinePath != null) {
                                    logger.info("成功保存基准图: {} -> {}", storedTaskId, baselinePath);
                                }
                            } catch (Exception e) {
                                logger.error("保存基准图失败: {}", storedTaskId, e);
                                // 即使保存基准图失败，仍然继续处理，返回成功的处理结果
                            }
                        }
                        
                        // 如果请求中有输出路径，则保存文件
                        String resultPath = null;
                        if (outputPath != null) {
                            resultPath = processor.saveProcessedImage(result, outputPath);
                        }

                        JSONObject resultData = new JSONObject();
                        resultData.put("taskId", taskId); // 使用前端传入的taskId
                        resultData.put("storedTaskId", storedTaskId); // 保存后端生成的taskId供调试
                        
                        // 添加基准图信息
                        if (baselinePath != null) {
                            resultData.put("isBaseline", true);
                            resultData.put("baselinePath", baselinePath);
                        }
                        
                        if (resultPath != null) {
                            resultData.put("outputPath", resultPath);
                        }
                        // 获取图像字节数据
                        byte[] imageData = null;
                        String format = "jpeg";
                        imageData = processor.getCachedImageBytes(taskId, 1);
                        String base64Image = "data:image/" + format + ";base64," + java.util.Base64.getEncoder().encodeToString(imageData);
                     //   resultData.put("data", base64Image);


                        sendTaskUpdate(conn, taskId, "completed", 100, resultData);
                        logger.info("图像调整成功: 前端taskId={}, 后端缓存taskId={}", taskId, storedTaskId);
                        
                        // 释放图像资源
                        result.release();
                    } else {
                        sendTaskUpdate(conn, taskId, "failed", 0, "图像调整失败");
                        logger.error("图像调整失败: taskId={}", taskId);
                    }
                } catch (Exception e) {
                    logger.error("执行任务时出错: taskId=" + taskId, e);
                    sendTaskUpdate(conn, taskId, "error", 0, "处理出错: " + e.getMessage());
                } finally {
                    // 移除活动任务
                    activeTasks.remove(taskId);
                }
            }
        });
    }
}