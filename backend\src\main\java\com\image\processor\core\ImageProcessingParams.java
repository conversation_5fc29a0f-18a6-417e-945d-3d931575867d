package com.image.processor.core;

/**
 * 图像处理参数类
 * 用于封装图像处理过程中的各种参数
 */
public class ImageProcessingParams {
    private double brightness = 0.0;
    private double contrast = 0.0; 
    private double saturation = 0.0;
    private double sharpness = 0.0;
    
    /**
     * 默认构造函数
     */
    public ImageProcessingParams() {
        // 使用默认值初始化
    }
    
    /**
     * 有参构造函数
     * 
     * @param brightness 亮度参数
     * @param contrast 对比度参数
     * @param saturation 饱和度参数
     * @param sharpness 锐化度参数
     */
    public ImageProcessingParams(double brightness, double contrast, double saturation, double sharpness) {
        this.brightness = brightness;
        this.contrast = contrast;
        this.saturation = saturation;
        this.sharpness = sharpness;
    }
    
    // Getters and Setters
    
    public double getBrightness() {
        return brightness;
    }
    
    public void setBrightness(double brightness) {
        this.brightness = brightness;
    }
    
    public double getContrast() {
        return contrast;
    }
    
    public void setContrast(double contrast) {
        this.contrast = contrast;
    }
    
    public double getSaturation() {
        return saturation;
    }
    
    public void setSaturation(double saturation) {
        this.saturation = saturation;
    }
    
    public double getSharpness() {
        return sharpness;
    }
    
    public void setSharpness(double sharpness) {
        this.sharpness = sharpness;
    }
    
    @Override
    public String toString() {
        return "ImageProcessingParams{" +
                "brightness=" + brightness +
                ", contrast=" + contrast +
                ", saturation=" + saturation +
                ", sharpness=" + sharpness +
                '}';
    }
} 