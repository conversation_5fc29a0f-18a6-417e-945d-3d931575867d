<template>
  <div 
    class="crop-overlay" 
    v-if="active" 
    :style="overlayStyle"
    @mousedown.stop="handleMouseDown"
    @mousemove.stop="handleMouseMove"
    @mouseup.stop="handleMouseUp"
    @mouseleave.stop="handleMouseUp"
  >
    <!-- 裁剪框 -->
    <div class="crop-frame" :style="frameStyle" style="pointer-events: auto;">
      <!-- 角部调整点 -->
      <div
        v-for="handle in handles"
        :key="handle.position"
        class="resize-handle"
        :class="handle.position"
        @mousedown.stop="startResize(handle.position, $event)"
      ></div>

      <!-- 网格线 -->
      <div class="crop-grid">
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  active: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    required: true
  },
  height: {
    type: Number,
    required: true
  },
  transform: {
    type: String,
    default: ''
  },
  initialCrop: {
    type: Object,
    default: () => null
  },
  cropRatio: {
    type: String,
    default: 'free'
  },
  originalImageWidth: {
    type: Number,
    required: true
  },
  originalImageHeight: {
    type: Number,
    required: true
  },
  overlayLeft: { type: Number, default: 0 },
  overlayTop: { type: Number, default: 0 },
});

const emit = defineEmits(['update:crop', 'crop-complete']);

// 状态
const cropRect = reactive({ x: 0, y: 0, width: 0, height: 0 });
const handles = [
  { position: 'top-left' }, { position: 'top-right' },
  { position: 'bottom-left' }, { position: 'bottom-right' }
];
const resizing = ref(false);
const dragging = ref(false);
const moving = ref(false);
const currentHandle = ref(null);
const startPoint = reactive({ x: 0, y: 0 });
const startRect = reactive({ x: 0, y: 0, width: 0, height: 0 });
const minSize = 20;

// 样式
const overlayStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
  transform: props.transform,
  position: 'absolute',
  left: 0,
  top: 0,
  pointerEvents: 'auto',
  zIndex: 10,
  cursor: 'crosshair'
}));

const frameStyle = computed(() => ({
  left: `${cropRect.x}px`,
  top: `${cropRect.y}px`,
  width: `${cropRect.width}px`,
  height: `${cropRect.height}px`,
  cursor: 'move'
}));

// 解析比例
function getCropRatio() {
  if (!props.cropRatio || props.cropRatio === 'free') return null;
  if (props.cropRatio === 'A4') {
    // A4纸比例，宽:高=210:297（纵向），如需横向可反过来
    return 210 / 297;
  }
  if (typeof props.cropRatio === 'string' && props.cropRatio.includes(':')) {
    const [w, h] = props.cropRatio.split(':').map(Number);
    if (w > 0 && h > 0) return w / h;
  }
  const num = Number(props.cropRatio);
  return isNaN(num) ? null : num;
}

// 初始化或重置裁剪区域
const initCropArea = () => {
  const ratio = getCropRatio();
  if (props.initialCrop && props.initialCrop.width > 0 && props.initialCrop.height > 0) {
    cropRect.x = props.initialCrop.x * props.width;
    cropRect.y = props.initialCrop.y * props.height;
    cropRect.width = props.initialCrop.width * props.width;
    cropRect.height = props.initialCrop.height * props.height;
    // 如果有比例限制，修正初始裁剪框
    if (ratio) {
      const expectHeight = cropRect.width / ratio;
      if (expectHeight > cropRect.height) {
        cropRect.height = expectHeight;
      } else {
        cropRect.width = cropRect.height * ratio;
      }
    }
  } else {
    // 默认居中，按比例生成
    let size = Math.min(props.width, props.height) * 0.8;
    let cropW = size, cropH = size;
    if (ratio) {
      if (props.width / props.height > ratio) {
        cropH = size;
        cropW = cropH * ratio;
      } else {
        cropW = size;
        cropH = cropW / ratio;
      }
    }
    cropRect.width = cropW;
    cropRect.height = cropH;
    cropRect.x = (props.width - cropW) / 2;
    cropRect.y = (props.height - cropH) / 2;
  }
  updateCropValue();
};

// 鼠标按下事件处理
const handleMouseDown = (e) => {
  const x = e.offsetX;
  const y = e.offsetY;
  if (isPointInCropFrame(x, y)) {
    // 点在裁剪框内部，开始移动
    moving.value = true;
    dragging.value = false;
    resizing.value = false;
    startPoint.x = e.clientX;
    startPoint.y = e.clientY;
    startRect.x = cropRect.x;
    startRect.y = cropRect.y;
    startRect.width = cropRect.width;
    startRect.height = cropRect.height;
  } else {
    // 点在裁剪框外部，开始创建新裁剪框
    dragging.value = true;
    moving.value = false;
    resizing.value = false;
    startPoint.x = e.offsetX;
    startPoint.y = e.offsetY;
    cropRect.x = e.offsetX;
    cropRect.y = e.offsetY;
    cropRect.width = 0;
    cropRect.height = 0;
  }
};

// 开始调整大小
const startResize = (handle, e) => {
  e.stopPropagation();
  resizing.value = true;
  dragging.value = false;
  moving.value = false;
  currentHandle.value = handle;
  startPoint.x = e.clientX;
  startPoint.y = e.clientY;
  startRect.x = cropRect.x;
  startRect.y = cropRect.y;
  startRect.width = cropRect.width;
  startRect.height = cropRect.height;
};

// 鼠标移动事件处理
const handleMouseMove = (e) => {
  const ratio = getCropRatio();
  if (dragging.value) {
    // 创建新裁剪框
    const currentX = Math.max(0, Math.min(e.offsetX, props.width));
    const currentY = Math.max(0, Math.min(e.offsetY, props.height));
    let x = Math.min(startPoint.x, currentX);
    let y = Math.min(startPoint.y, currentY);
    let w = Math.abs(currentX - startPoint.x);
    let h = Math.abs(currentY - startPoint.y);
    if (ratio) {
      // 拖拽时锁定比例
      if (w / h > ratio) {
        w = h * ratio;
      } else {
        h = w / ratio;
      }
      // 修正位置
      if (currentX < startPoint.x) x = startPoint.x - w;
      if (currentY < startPoint.y) y = startPoint.y - h;
    }
    cropRect.x = Math.max(0, Math.min(x, props.width - w));
    cropRect.y = Math.max(0, Math.min(y, props.height - h));
    cropRect.width = Math.max(minSize, Math.min(w, props.width - cropRect.x));
    cropRect.height = Math.max(minSize, Math.min(h, props.height - cropRect.y));
    updateCropValue();
  } else if (moving.value) {
    // 移动裁剪框
    const dx = e.clientX - startPoint.x;
    const dy = e.clientY - startPoint.y;
    cropRect.x = Math.max(0, Math.min(startRect.x + dx, props.width - cropRect.width));
    cropRect.y = Math.max(0, Math.min(startRect.y + dy, props.height - cropRect.height));
    updateCropValue();
  } else if (resizing.value) {
    // 调整大小
    let newX = startRect.x;
    let newY = startRect.y;
    let newWidth = startRect.width;
    let newHeight = startRect.height;
    const dx = e.clientX - startPoint.x;
    const dy = e.clientY - startPoint.y;
    switch (currentHandle.value) {
      case 'top-left':
        if (ratio) {
          // 锁定比例
          let delta = Math.min(dx, dy);
          newX = startRect.x + delta;
          newY = startRect.y + delta / ratio;
          newWidth = startRect.width - delta;
          newHeight = startRect.height - delta / ratio;
        } else {
          newX = startRect.x + dx;
          newY = startRect.y + dy;
          newWidth = startRect.width - dx;
          newHeight = startRect.height - dy;
        }
        break;
      case 'top-right':
        if (ratio) {
          let delta = Math.min(-dx, dy);
          newY = startRect.y + delta / ratio;
          newWidth = startRect.width + dx;
          newHeight = newWidth / ratio;
        } else {
          newY = startRect.y + dy;
          newWidth = startRect.width + dx;
          newHeight = startRect.height - dy;
        }
        break;
      case 'bottom-left':
        if (ratio) {
          let delta = Math.min(dx, -dy);
          newX = startRect.x + delta;
          newWidth = startRect.width - delta;
          newHeight = newWidth / ratio;
        } else {
          newX = startRect.x + dx;
          newWidth = startRect.width - dx;
          newHeight = startRect.height + dy;
        }
        break;
      case 'bottom-right':
        if (ratio) {
          newWidth = startRect.width + dx;
          newHeight = newWidth / ratio;
        } else {
          newWidth = startRect.width + dx;
          newHeight = startRect.height + dy;
        }
        break;
    }
    // 限制最小尺寸
    if (newWidth < minSize) {
      if (currentHandle.value && currentHandle.value.includes('left')) {
        newX = startRect.x + startRect.width - minSize;
      }
      newWidth = minSize;
      if (ratio) newHeight = minSize / ratio;
    }
    if (newHeight < minSize) {
      if (currentHandle.value && currentHandle.value.includes('top')) {
        newY = startRect.y + startRect.height - minSize;
      }
      newHeight = minSize;
      if (ratio) newWidth = minSize * ratio;
    }
    // 边界
    if (newX < 0) {
      newWidth += newX;
      newX = 0;
    }
    if (newY < 0) {
      newHeight += newY;
      newY = 0;
    }
    if (newX + newWidth > props.width) {
      newWidth = props.width - newX;
      if (ratio) newHeight = newWidth / ratio;
    }
    if (newY + newHeight > props.height) {
      newHeight = props.height - newY;
      if (ratio) newWidth = newHeight * ratio;
    }
    cropRect.x = newX;
    cropRect.y = newY;
    cropRect.width = newWidth;
    cropRect.height = newHeight;
    updateCropValue();
  }
};

// 鼠标抬起
const handleMouseUp = () => {
  if (resizing.value || dragging.value || moving.value) {
    emit('crop-complete', getNormalizedCropRect());
  }
  dragging.value = false;
  moving.value = false;
  resizing.value = false;
  currentHandle.value = null;
};

// 判断点是否在裁剪框内部（不含边框和手柄）
const isPointInCropFrame = (x, y) => {
  const margin = 10;
  return x > cropRect.x + margin &&
    x < cropRect.x + cropRect.width - margin &&
    y > cropRect.y + margin &&
    y < cropRect.y + cropRect.height - margin;
};

// 获取归一化坐标
const getNormalizedCropRect = () => {
  return {
    x: cropRect.x / props.width,
    y: cropRect.y / props.height,
    width: cropRect.width / props.width,
    height: cropRect.height / props.height
  };
};

// 更新裁剪值
const updateCropValue = () => {
  emit('update:crop', getNormalizedCropRect());
};

// 监听器
watch(() => props.active, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initCropArea();
    });
  } else {
    resizing.value = false;
    dragging.value = false;
    moving.value = false;
  }
});

watch(() => props.cropRatio, (newVal, oldVal) => {
  const ratio = getCropRatio();
  if (!ratio) return; // free模式不处理
  // 以当前中心为基准，调整为新比例
  const centerX = cropRect.x + cropRect.width / 2;
  const centerY = cropRect.y + cropRect.height / 2;
  let newWidth = cropRect.width;
  let newHeight = cropRect.height;
  // 以当前宽为主，算高
  newHeight = newWidth / ratio;
  if (newHeight > props.height) {
    newHeight = props.height;
    newWidth = newHeight * ratio;
  }
  if (newWidth > props.width) {
    newWidth = props.width;
    newHeight = newWidth / ratio;
  }
  // 保持中心
  let newX = centerX - newWidth / 2;
  let newY = centerY - newHeight / 2;
  // 边界修正
  newX = Math.max(0, Math.min(newX, props.width - newWidth));
  newY = Math.max(0, Math.min(newY, props.height - newHeight));
  cropRect.x = newX;
  cropRect.y = newY;
  cropRect.width = newWidth;
  cropRect.height = newHeight;
  updateCropValue();
});

// 初始化
onMounted(() => {
  if (props.active) {
    initCropArea();
  }
});

</script>

<style scoped>
.crop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  user-select: none;
  z-index: 10;
  pointer-events: auto;
}

.crop-frame {
  position: absolute;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  z-index: 11;
  transform: translate(-50%, -50%);
  pointer-events: auto;
}

.top-left { top: 0; left: 0; cursor: nwse-resize; }
.top-right { top: 0; right: 0; cursor: nesw-resize; transform: translate(50%, -50%); }
.bottom-left { bottom: 0; left: 0; cursor: nesw-resize; transform: translate(-50%, 50%); }
.bottom-right { bottom: 0; right: 0; cursor: nwse-resize; transform: translate(50%, 50%); }

.crop-grid {
  position: absolute;
  top: 0; left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.grid-line {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 2px rgba(0,0,0,0.3);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
  top: 33.33%;
}
.grid-line.horizontal:nth-of-type(2) {
  top: 66.66%;
}

.grid-line.vertical {
  height: 100%;
  width: 1px;
  left: 33.33%;
}
.grid-line.vertical:nth-of-type(4) {
  left: 66.66%;
}
</style>