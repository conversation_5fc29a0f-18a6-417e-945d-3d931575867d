package com.image.processor.core;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.imageio.ImageIO;

import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import static org.bytedeco.opencv.global.opencv_imgcodecs.IMWRITE_JPEG_QUALITY;
import static org.bytedeco.opencv.global.opencv_imgcodecs.imwrite;
import static org.bytedeco.opencv.global.opencv_imgproc.INTER_AREA;
import static org.bytedeco.opencv.global.opencv_imgproc.resize;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.image.processor.utils.TaskIdUtils;
import com.image.processor.utils.TransformationMapper;

/**
 * 缩略图生成器，使用JavaCV处理图像
 */
public class ThumbnailGenerator {
    private static final Logger logger = LoggerFactory.getLogger(ThumbnailGenerator.class);
    
    // 默认参数
    private static final int DEFAULT_QUALITY = 85;
    private static final boolean DEFAULT_KEEP_ASPECT_RATIO = true;
    private static final String DEFAULT_RESIZE_MODE = "fit";
    // 使用TransformationMapper中的常量
    private static final int DEFAULT_THUMBNAIL_WIDTH = TransformationMapper.THUMBNAIL_MAX_DIMENSION;
    private static final int DEFAULT_THUMBNAIL_HEIGHT = TransformationMapper.THUMBNAIL_MAX_DIMENSION;
    
    // 支持的图片扩展名
    private static final String[] SUPPORTED_EXTENSIONS = {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"
    };

    // 文件级别的锁映射，确保同一时间只有一个线程处理同一个文件
    private static final Map<String, Object> FILE_LOCKS = new ConcurrentHashMap<>();

    /**
     * 生成缩略图路径
     * @param originalPath 原图路径
     * @param thumbnailDir 缩略图存储目录
     * @param scanDirectory 扫描目录（用于计算相对路径）
     * @return 缩略图路径
     */
    public static String generateThumbnailPath(String originalPath, String thumbnailDir, String scanDirectory) {
        File originalFile = new File(originalPath);
        if (!originalFile.exists()) {
            logger.error("原图文件不存在: {}", originalPath);
            return null;
        }
        
        // 计算文件哈希值
        String fileHash = TaskIdUtils.calculateFileHash(originalFile);
        if (fileHash == null) {
            logger.error("无法计算文件哈希值: {}", originalPath);
            return null;
        }
        
        // 获取文件扩展名
        String fileName = originalFile.getName();
        String ext = fileName.substring(fileName.lastIndexOf('.'));
        
        // 计算相对路径 - 修改为包含扫描目录名称
        String relativePath;
        
        if (scanDirectory != null && !scanDirectory.isEmpty()) {
            try {
                // 标准化路径
                String normalizedOriginalPath = TaskIdUtils.normalizePath(originalPath);
                String normalizedScanDirectory = TaskIdUtils.normalizePath(scanDirectory);
                
                // 获取扫描目录的父目录
                File scanDir = new File(scanDirectory);
                File scanParent = scanDir.getParentFile();
                
                // 计算相对于扫描目录父级的路径，这样可以包含扫描目录名称
                if (scanParent != null) {
                    Path original = Paths.get(normalizedOriginalPath).toAbsolutePath().getParent();
                    Path parent = Paths.get(TaskIdUtils.normalizePath(scanParent.getAbsolutePath())).toAbsolutePath();
                    relativePath = parent.relativize(original).toString();
                } else {
                    // 扫描目录没有父目录，使用原始方法
                    Path scan = Paths.get(normalizedScanDirectory).toAbsolutePath();
                    Path original = Paths.get(normalizedOriginalPath).toAbsolutePath().getParent();
                    relativePath = scan.getFileName() + File.separator + scan.relativize(original);
                }
            } catch (Exception e) {
                // 如果计算失败，回退到原始方法
                logger.warn("计算包含扫描目录名称的相对路径失败: {}", e.getMessage());
                Path scan = Paths.get(scanDirectory).toAbsolutePath();
                Path original = Paths.get(originalPath).toAbsolutePath().getParent();
                relativePath = scan.relativize(original).toString();
            }
        } else {
            // 如果没有扫描目录，使用原始方法
            Path original = Paths.get(originalPath).toAbsolutePath().getParent();
            relativePath = original.getFileName().toString();
            if (original.getParent() != null) {
                relativePath = original.getParent().getFileName() + File.separator + relativePath;
            }
        }
        
        // 构建缩略图路径，并确保使用标准化路径
        return TaskIdUtils.normalizePath(Paths.get(thumbnailDir, relativePath, fileHash + "_thumb" + ext).toString());
    }
    
  
    
    /**
     * 便捷方法：确保单个图像的缩略图存在，如果不存在则生成
     * 简化版本，直接指定原图路径和缩略图路径
     * 
     * @param originalPath 原始图像路径
     * @param thumbnailPath 缩略图路径
     * @return 成功返回true，失败返回false
     */
    public static boolean ensureThumbnail(String originalPath, String thumbnailPath) {
        logger.info("确保缩略图存在: 原图={}, 缩略图={}", originalPath, thumbnailPath);
        
        // 检查原图是否存在
        File originalFile = new File(originalPath);
        if (!originalFile.exists() || !originalFile.isFile()) {
            logger.error("原图不存在或不是文件: {}", originalPath);
            return false;
        }
        
        // 检查缩略图是否已存在
        File thumbnailFile = new File(thumbnailPath);
        if (thumbnailFile.exists() && thumbnailFile.length() > 0) {
            logger.info("缩略图已存在: {}", thumbnailPath);
            return true;
        }
        
        // 确保目标目录存在
        File outputDir = thumbnailFile.getParentFile();
        if (outputDir != null && !outputDir.exists()) {
            if (!outputDir.mkdirs()) {
                logger.error("无法创建缩略图目录: {}", outputDir.getAbsolutePath());
                return false;
            }
        }
        
        // 生成缩略图
        ThumbnailGenerator generator = new ThumbnailGenerator();
        boolean success = generator.generateThumbnail(
            originalPath,
            thumbnailPath,
            DEFAULT_THUMBNAIL_WIDTH,
            DEFAULT_THUMBNAIL_HEIGHT
        );
        
        if (success) {
            logger.info("成功生成缩略图: {}", thumbnailPath);
            return true;
        } else {
            logger.error("生成缩略图失败: {}", thumbnailPath);
            return false;
        }
    }
    
    /**
     * 生成图像的缩略图
     *
     * @param inputPath  输入图像文件路径
     * @param outputPath 输出缩略图文件路径
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 成功返回true，失败返回false
     */
    public boolean generateThumbnail(String inputPath, String outputPath, int targetWidth, int targetHeight) {
        return generateThumbnail(inputPath, outputPath, targetWidth, targetHeight, 
                               DEFAULT_QUALITY, DEFAULT_KEEP_ASPECT_RATIO, DEFAULT_RESIZE_MODE, null);
    }
    
    /**
     * 生成图像的缩略图，带进度回调
     *
     * @param inputPath  输入图像文件路径
     * @param outputPath 输出缩略图文件路径
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @param progressCallback 进度回调
     * @return 成功返回true，失败返回false
     */
    public boolean generateThumbnail(String inputPath, String outputPath, int targetWidth, 
                                  int targetHeight, ProgressCallback progressCallback) {
        return generateThumbnail(inputPath, outputPath, targetWidth, targetHeight, 
                               DEFAULT_QUALITY, DEFAULT_KEEP_ASPECT_RATIO, DEFAULT_RESIZE_MODE, 
                               progressCallback);
    }
    
    /**
     * 生成图像的缩略图，带完整参数
     *
     * @param inputPath  输入图像文件路径
     * @param outputPath 输出缩略图文件路径
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @param quality 输出图像质量 (0-100)
     * @param keepAspectRatio 是否保持宽高比
     * @param resizeMode 调整模式 ("fit" 或 "fill")
     * @param progressCallback 进度回调
     * @return 成功返回true，失败返回false
     */
    public boolean generateThumbnail(String inputPath, String outputPath, int targetWidth, 
                                  int targetHeight, int quality, boolean keepAspectRatio,
                                  String resizeMode, ProgressCallback progressCallback) {
        logger.info("开始生成缩略图: input={}, output={}, size={}x{}, quality={}, keepAspectRatio={}, resizeMode={}", 
                   inputPath, outputPath, targetWidth, targetHeight, quality, keepAspectRatio, resizeMode);
        
        // 获取文件锁，确保同一个文件同一时间只有一个线程在处理
        Object fileLock = FILE_LOCKS.computeIfAbsent(inputPath, k -> new Object());
        
        // 声明所有资源变量，以便在finally块中释放
        Mat image = null;
        Mat thumbnail = null;
        File tempFile = null;
        boolean success = false;
        
        // 使用同步块确保线程安全
        synchronized (fileLock) {
            try {
                // 进度报告
                reportProgress(progressCallback, 0, "开始读取图像文件");
                
                // 检查输出文件是否已存在
                File outputFile = new File(outputPath);
                if (outputFile.exists() && outputFile.length() > 0) {
                    logger.info("缩略图已存在,无需重新生成: {}", outputPath);
                    reportProgress(progressCallback, 100, "缩略图已存在,无需重新生成");
                    return true;
                }
                
                // 验证输入文件
                File inputFile = new File(inputPath);
                if (!inputFile.exists() || !inputFile.isFile()) {
                    logger.error("输入文件不存在: {}", inputPath);
                    reportProgress(progressCallback, -1, "输入文件不存在: " + inputPath);
                    return false;
                }
                
                if (!inputFile.canRead()) {
                    logger.error("输入文件不可读: {}", inputPath);
                    reportProgress(progressCallback, -1, "输入文件不可读: " + inputPath);
                    return false;
                }
                
                // 确保输出目录存在
                File outputDir = outputFile.getParentFile();
                if (outputDir != null && !outputDir.exists()) {
                    if (!outputDir.mkdirs()) {
                        logger.error("无法创建输出目录: {}", outputDir.getAbsolutePath());
                        reportProgress(progressCallback, -1, "无法创建输出目录: " + outputDir.getAbsolutePath());
                        return false;
                    }
                }
                
                // 1. 读取图像
                reportProgress(progressCallback, 10, "正在读取图像");
                logger.debug("尝试读取图像文件: {}", inputPath);
                
                // 检查文件是否存在和可读
                if (!inputFile.exists()) {
                    logger.error("文件不存在: {}", inputFile.getAbsolutePath());
                    reportProgress(progressCallback, -1, "文件不存在: " + inputPath);
                    return false;
                }
                logger.debug("文件存在，大小: {} 字节", inputFile.length());
                
                if (!inputFile.canRead()) {
                    logger.error("文件不可读: {}", inputFile.getAbsolutePath());
                    reportProgress(progressCallback, -1, "文件不可读: " + inputPath);
                    return false;
                }
                logger.debug("文件可读");
                
                // 使用绝对路径
                String absolutePath = inputFile.getAbsolutePath();
                logger.debug("使用绝对路径: {}", absolutePath);
                
                // 检查文件大小
                long fileSize = inputFile.length();
                logger.debug("文件大小: {} 字节", fileSize);
                if (fileSize == 0) {
                    logger.error("文件大小为0: {}", absolutePath);
                    reportProgress(progressCallback, -1, "文件大小为0: " + inputPath);
                    return false;
                }
                
                // 使用Java原生ImageIO加载图像
                logger.debug("开始使用Java原生ImageIO读取图像");
                BufferedImage bufferedImage = null;
                try {
                    bufferedImage = ImageIO.read(inputFile);
                    
                    if (bufferedImage == null) {
                        logger.error("Java ImageIO无法读取图像: {}", absolutePath);
                        reportProgress(progressCallback, -1, "Java ImageIO无法读取图像: " + inputPath);
                        return false;
                    }
                    
                    logger.debug("成功使用Java读取图像, 尺寸: {}x{}", 
                               bufferedImage.getWidth(), bufferedImage.getHeight());
                } catch (IOException e) {
                    logger.error("Java读取图像时出错", e);
                    reportProgress(progressCallback, -1, "Java读取图像时出错: " + e.getMessage());
                    return false;
                }
                
                // 将BufferedImage转换为OpenCV的Mat
                try {
                    logger.debug("开始将BufferedImage转换为OpenCV Mat");
                    Java2DFrameConverter java2dConverter = new Java2DFrameConverter();
                    OpenCVFrameConverter.ToMat matConverter = new OpenCVFrameConverter.ToMat();
                    image = matConverter.convert(java2dConverter.convert(bufferedImage));
                    logger.debug("BufferedImage转换为Mat完成");
                    
                    // 立即释放Java内存以减轻压力
                    bufferedImage.flush();
                    bufferedImage = null;
                    System.gc(); // 提示JVM进行垃圾回收
                    
                    if (image == null || image.empty()) {
                        logger.error("转换后的OpenCV图像为空: {}", absolutePath);
                        reportProgress(progressCallback, -1, "转换后的OpenCV图像为空: " + inputPath);
                        return false;
                    }
                    
                    logger.debug("成功转换为OpenCV图像, 尺寸: {}x{}", image.cols(), image.rows());
                    
                    // 2. 计算目标尺寸
                    int originalWidth = image.cols();
                    int originalHeight = image.rows();
                    logger.debug("原始图像尺寸: {}x{}", originalWidth, originalHeight);
                    
                    reportProgress(progressCallback, 20, "计算目标尺寸");
                    
                    // 检查原图是否已经小于或接近目标尺寸，如果是则直接使用原图
                    boolean needsResize = true;
                    if (originalWidth <= targetWidth && originalHeight <= targetHeight) {
                        logger.info("原图尺寸小于目标缩略图尺寸，将直接复制原图");
                        reportProgress(progressCallback, 40, "原图已经足够小，将直接复制原图");
                        needsResize = false;
                    }
                    
                    int newWidth = targetWidth;
                    int newHeight = targetHeight;
                    
                    if (needsResize) {
                        if (keepAspectRatio) {
                            // 使用TransformationMapper计算缩略图尺寸
                            double ratio = TransformationMapper.calculateScaleRatio(originalWidth, originalHeight);
                            newWidth = (int) Math.round(originalWidth * ratio);
                            newHeight = (int) Math.round(originalHeight * ratio);
                            
                            // 如果开发者指定了特定尺寸，而不是使用默认值，则使用原始计算方法
                            if (targetWidth != DEFAULT_THUMBNAIL_WIDTH || targetHeight != DEFAULT_THUMBNAIL_HEIGHT) {
                                double widthRatio = (double) targetWidth / originalWidth;
                                double heightRatio = (double) targetHeight / originalHeight;
                                double resizeRatio = "fit".equals(resizeMode) ? 
                                    Math.min(widthRatio, heightRatio) : Math.max(widthRatio, heightRatio);
                                
                                newWidth = (int) Math.round(originalWidth * resizeRatio);
                                newHeight = (int) Math.round(originalHeight * resizeRatio);
                            }
                        }
                        
                        logger.debug("调整后的尺寸: {}x{}", newWidth, newHeight);
                    } else {
                        newWidth = originalWidth;
                        newHeight = originalHeight;
                        logger.debug("使用原始尺寸: {}x{}", newWidth, newHeight);
                    }
                    
                    // 检查是否应该取消任务
                    if (!checkContinue(progressCallback)) return false;
                    
                    // 3. 调整图像大小或直接使用原图
                    try {
                        if (needsResize) {
                            reportProgress(progressCallback, 40, "调整图像大小");
                            thumbnail = new Mat();
                            Size newSize = new Size(newWidth, newHeight);
                            resize(image, thumbnail, newSize, 0, 0, INTER_AREA);
                            
                            // 安全检查
                            if (thumbnail == null || thumbnail.empty()) {
                                logger.error("调整大小后的图像为空");
                                reportProgress(progressCallback, -1, "调整大小后的图像为空");
                                return false;
                            }
                        } else {
                            // 如果不需要调整大小，直接使用原图
                            reportProgress(progressCallback, 40, "使用原图");
                            thumbnail = image.clone();
                        }
                        
                        // 4. 保存缩略图
                        reportProgress(progressCallback, 80, "保存缩略图");
                        
                        // 创建临时文件名，不包含中文
                        String tempFileName = getTempFileName(inputFile);
                        tempFile = new File(System.getProperty("java.io.tmpdir"), tempFileName);
                        logger.debug("创建临时文件: {}", tempFile.getAbsolutePath());
                        
                        // 设置JPEG压缩质量
                        int[] params = new int[] { IMWRITE_JPEG_QUALITY, quality };
                        
                        // 写入临时文件然后复制到目标路径
                        logger.debug("写入临时文件: {}", tempFile.getAbsolutePath());
                        boolean tempSuccess = imwrite(tempFile.getAbsolutePath(), thumbnail, params);
                        
                        if (tempSuccess) {
                            logger.debug("写入临时文件成功，开始复制到目标路径: {}", outputPath);
                            
                            // 确保目标目录存在
                            if (outputDir != null && !outputDir.exists()) {
                                outputDir.mkdirs();
                            }
                            
                            // 使用Java NIO复制文件
                            java.nio.file.Files.copy(
                                tempFile.toPath(),
                                outputFile.toPath(),
                                java.nio.file.StandardCopyOption.REPLACE_EXISTING
                            );
                            
                            // 检查复制是否成功
                            if (outputFile.exists() && outputFile.length() > 0) {
                                logger.debug("复制到目标路径成功，文件大小: {} 字节", outputFile.length());
                                success = true;
                            } else {
                                logger.error("复制到目标路径失败，目标文件不存在或大小为0");
                            }
                        } else {
                            logger.error("写入临时文件失败");
                        }
                    } catch (Exception e) {
                        logger.error("处理图像时出错: {}", e.getMessage(), e);
                        reportProgress(progressCallback, -1, "处理图像时出错: " + e.getMessage());
                        return false;
                    }
                } catch (Exception e) {
                    logger.error("转换图像格式时出错", e);
                    reportProgress(progressCallback, -1, "转换图像格式时出错: " + e.getMessage());
                    return false;
                }
                
                if (success) {
                    reportProgress(progressCallback, 100, "缩略图生成完成");
                    logger.info("缩略图生成成功: {}", outputPath);
                } else {
                    logger.error("保存缩略图失败: {}", outputPath);
                    reportProgress(progressCallback, -1, "保存缩略图失败");
                }
                
                return success;
                
            } catch (Exception e) {
                logger.error("生成缩略图时出错", e);
                reportProgress(progressCallback, -1, "生成缩略图时出错: " + e.getMessage());
                return false;
            } finally {
                // 释放文件锁
                FILE_LOCKS.remove(inputPath);
                
                // 释放所有资源
                if (image != null && !image.isNull()) {
                    try {
                        image.release();
                        image = null;
                    } catch (Exception e) {
                        logger.error("释放图像资源时出错", e);
                    }
                }
                
                if (thumbnail != null && !thumbnail.isNull()) {
                    try {
                        thumbnail.release();
                        thumbnail = null;
                    } catch (Exception e) {
                        logger.error("释放缩略图资源时出错", e);
                    }
                }
                
                // 删除临时文件
                if (tempFile != null && tempFile.exists()) {
                    try {
                        boolean deleted = tempFile.delete();
                        logger.debug("删除临时文件{}: {}", deleted ? "成功" : "失败", tempFile.getAbsolutePath());
                    } catch (Exception e) {
                        logger.error("删除临时文件时出错", e);
                    }
                }
                
                // 提示JVM进行垃圾回收
                System.gc();
            }
        }
    }
    
    /**
     * 直接复制原图到缩略图位置，不进行大小调整
     * 当原图尺寸已经小于目标缩略图尺寸时使用此方法
     * 
     * @param inputPath 输入图像文件路径
     * @param outputPath 输出缩略图文件路径
     * @param progressCallback 进度回调
     * @return 成功返回true，失败返回false
     */
    public boolean copyOriginalToThumbnail(String inputPath, String outputPath, ProgressCallback progressCallback) {
        logger.info("直接复制原图到缩略图位置: input={}, output={}", inputPath, outputPath);
        
        try {
            reportProgress(progressCallback, 0, "开始复制原图到缩略图位置");
            
            // 检查输出文件是否已存在
            File outputFile = new File(outputPath);
            if (outputFile.exists() && outputFile.length() > 0) {
                logger.info("缩略图已存在,无需复制: {}", outputPath);
                reportProgress(progressCallback, 100, "缩略图已存在,无需复制");
                return true;
            }
            
            // 验证输入文件
            File inputFile = new File(inputPath);
            if (!inputFile.exists() || !inputFile.isFile()) {
                logger.error("输入文件不存在: {}", inputPath);
                reportProgress(progressCallback, -1, "输入文件不存在: " + inputPath);
                return false;
            }
            
            if (!inputFile.canRead()) {
                logger.error("输入文件不可读: {}", inputPath);
                reportProgress(progressCallback, -1, "输入文件不可读: " + inputPath);
                return false;
            }
            
            // 确保输出目录存在
            File outputDir = outputFile.getParentFile();
            if (outputDir != null && !outputDir.exists()) {
                if (!outputDir.mkdirs()) {
                    logger.error("无法创建输出目录: {}", outputDir.getAbsolutePath());
                    reportProgress(progressCallback, -1, "无法创建输出目录: " + outputDir.getAbsolutePath());
                    return false;
                }
            }
            
            reportProgress(progressCallback, 50, "正在复制文件");
            
            // 直接复制文件
            java.nio.file.Files.copy(
                inputFile.toPath(),
                outputFile.toPath(),
                java.nio.file.StandardCopyOption.REPLACE_EXISTING
            );
            
            // 检查复制是否成功
            if (outputFile.exists() && outputFile.length() > 0) {
                logger.info("复制原图到缩略图位置成功: {}", outputPath);
                reportProgress(progressCallback, 100, "复制成功");
                return true;
            } else {
                logger.error("复制原图到缩略图位置失败: {}", outputPath);
                reportProgress(progressCallback, -1, "复制失败");
                return false;
            }
            
        } catch (Exception e) {
            logger.error("复制原图到缩略图位置时出错", e);
            reportProgress(progressCallback, -1, "复制时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 报告进度
     */
    private void reportProgress(ProgressCallback callback, int progress, String message) {
        if (callback != null) {
            logger.debug("进度: {}%, {}", progress, message);
            callback.onProgress(progress, message);
        }
    }
    
    /**
     * 检查是否应该继续处理
     * @return 如果应该继续返回true，否则返回false
     */
    private boolean checkContinue(ProgressCallback callback) {
        if (callback == null) {
            return true;
        }
        
        return callback.onProgress(-1, "检查是否取消");
    }

    /**
     * 处理目录中的所有图片文件
     */
    public List<ThumbnailResult> processDirectory(String directoryPath, String thumbnailDir, String scanDirectory, ProgressCallback callback) {
        List<ThumbnailResult> results = new ArrayList<>();
        
        // 检查目录是否存在
        File dir = new File(directoryPath);
        if (!dir.exists() || !dir.isDirectory()) {
            throw new IllegalArgumentException("目录不存在或不是有效的目录: " + directoryPath);
        }

        try {
            // 查找所有图片文件
            List<File> imageFiles = findImageFiles(dir);
            int totalFiles = imageFiles.size();
            logger.info("在目录 {} 中找到 {} 个图片文件", directoryPath, totalFiles);

            // 处理每个图片文件
            for (int i = 0; i < totalFiles; i++) {
                File imageFile = imageFiles.get(i);
                try {
                    // 计算进度
                    int progress = (int) ((i + 1) * 100.0 / totalFiles);
                    String message = String.format("正在处理 %d/%d: %s", i + 1, totalFiles, imageFile.getName());
                    
                    // 更新进度
                    if (callback != null && !callback.onProgress(progress, message)) {
                        logger.info("处理被用户取消");
                        break;
                    }

                    // 生成缩略图
                    String thumbnailPath = generateThumbnailPath(imageFile.getAbsolutePath(), thumbnailDir, scanDirectory);
                    
                    // 检查缩略图是否已存在
                    File thumbnailFile = new File(thumbnailPath);
                    boolean alreadyExists = thumbnailFile.exists() && thumbnailFile.length() > 0;
                    boolean success;
                    
                    if (alreadyExists) {
                        logger.info("缩略图已存在,无需重新生成: {}", thumbnailPath);
                        success = true;
                    } else {
                        // 生成缩略图 - 使用新的默认尺寸
                        success = generateThumbnail(
                            imageFile.getAbsolutePath(),
                            thumbnailPath,
                            DEFAULT_THUMBNAIL_WIDTH, DEFAULT_THUMBNAIL_HEIGHT, 85, true, "fit",
                            null // 内部处理不需要进度回调
                        );
                    }

                    // 记录结果
                    results.add(new ThumbnailResult(
                        imageFile.getAbsolutePath(),
                        success,
                        success ? (alreadyExists ? "缩略图已存在" : "处理成功") : "处理失败"
                    ));

                } catch (Exception e) {
                    logger.error("处理文件时出错: " + imageFile.getAbsolutePath(), e);
                    results.add(new ThumbnailResult(
                        imageFile.getAbsolutePath(),
                        false,
                        "处理出错: " + e.getMessage()
                    ));
                }
            }
        } catch (Exception e) {
            logger.error("处理目录时出错: " + directoryPath, e);
            throw new RuntimeException("处理目录失败: " + e.getMessage(), e);
        }
        
        return results;
    }
    
    /**
     * 递归查找目录中的所有图片文件
     * @param directory 要搜索的目录
     * @return 图片文件列表
     */
    private List<File> findImageFiles(File directory) {
        List<File> imageFiles = new ArrayList<>();
        File[] files = directory.listFiles();
        
        if (files == null) {
            return imageFiles;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                imageFiles.addAll(findImageFiles(file));
            } else if (file.isFile()) {
                // 检查文件扩展名
                String fileName = file.getName().toLowerCase();
                for (String ext : SUPPORTED_EXTENSIONS) {
                    if (fileName.endsWith(ext)) {
                        imageFiles.add(file);
                        break;
                    }
                }
            }
        }
        
        return imageFiles;
    }
    
    /**
     * 缩略图处理结果
     */
    public static class ThumbnailResult {
        private final String imagePath;
        private final boolean success;
        private final String message;
        
        public ThumbnailResult(String imagePath, boolean success, String message) {
            this.imagePath = imagePath;
            this.success = success;
            this.message = message;
        }
        
        public String getImagePath() {
            return imagePath;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
    }

    private String getTempFileName(File inputFile) {
        // 使用TaskIdUtils计算文件哈希
        String fileHash = TaskIdUtils.calculateFileHash(inputFile);
        return fileHash + "_" + System.currentTimeMillis() + ".jpg";
    }
} 