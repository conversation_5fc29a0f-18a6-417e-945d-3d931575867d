<template>
  <el-config-provider :locale="zhCn">
    <div class="app-container dark-theme">
      <el-container>
        <!-- 主体内容区 -->
        <el-container class="main-content">
          <!-- 左侧文件导航区 -->
          <el-aside width="260px" class="file-explorer">
            <file-explorer
                :directory="currentDirectory"
                @select-file="handleFileSelect"
                @open-directory="openDirectory"
            />
          </el-aside>

          <!-- 中间图像编辑区 -->
          <el-main class="image-workspace">
            <image-workspace
                ref="imageWorkspaceRef"
                :selected-file="selectedFile"
                :dispaly-file="dispalyFile"
                :edit-params="editParams"
                v-model:currentBaselineImage="currentBaselineImage"
                @update-params="handleParamsUpdate"
                @operation-applied="handleOperationApplied"
                @update-image-url-display-base64="refreshBase64UrlImageDisplay"
            />
          </el-main>

          <!-- 右侧工具控制区 -->
          <el-aside width="280px" class="tool-panel">
            <tool-panel
                ref="toolPanelRef"
                v-model:currentBaselineImage="currentBaselineImage"
                :selected-image="selectedFile"
                :current-params="editParams"
                @update-params="handleParamsUpdate"
                @apply-edit="refreshImageDisplay"
                @apply-base64Url="refreshBase64UrlImageDisplay"
                @set-tool="handleToolChange"
                @open-settings="openSettingsDialog"
            />
          </el-aside>
        </el-container>

        <!-- 底部状态栏 -->
        <el-footer height="30px" class="app-footer">
          <div class="footer-left">
            <p>图像处理工具</p>
          </div>
          <div class="footer-right">
            <p>{{ selectedFile ? '已选择: ' + getFileName(selectedFile) : '未选择文件' }}</p>
          </div>
          <div class="footer">
            <el-progress
                :percentage="progress"
                :format="progressFormat"
                :status="progressStatus"
                :stroke-width="4"
                :show-text="true"
            />
          </div>
        </el-footer>
      </el-container>

      <!-- 设置对话框 -->
      <settings-dialog v-model:visible="settingsDialogVisible"/>

    </div>
  </el-config-provider>
</template>

<script setup>
import {ref, reactive, onMounted, computed, onUnmounted, nextTick, watch} from 'vue';
import {ElConfigProvider, ElMessage} from 'element-plus';
import {Folder} from '@element-plus/icons-vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import FileExplorer from './components/FileExplorer.vue';
import ImageWorkspace from './components/ImageWorkspace.vue';
import ToolPanel from './components/ToolPanel/index.vue';
import SettingsDialog from './views/SettingsDialog.vue';
import {useRouter} from 'vue-router';
import historyService from "@/services/history-service";

const appVersion = ref('');
const currentDirectory = ref('');
const selectedFile = ref('');
const dispalyFile = ref('');
const editParams = reactive({
  brightness: 0,
  contrast: 0,
  saturation: 0,
  sharpness: 0,
  crop: null,
  rotate: 0
});
const currentBaselineImage = ref('');

// 调试：监听基准图变化
watch(currentBaselineImage, (val) => {
  console.log('App.vue currentBaselineImage 变化:', val);
});

// 控制设置对话框显示
const settingsDialogVisible = ref(false);
const openSettingsDialog = () => {
  settingsDialogVisible.value = true;
};

// 获取文件名
const getFileName = (path) => {
  if (!path) return '';
  return path.split(/[\/\\]/).pop();
};

// 进度条相关
const progress = ref(0);
const progressStatus = ref('');
const progressMessage = ref('');
const totalFiles = ref(0);
const processedFiles = ref(0);

// 格式化进度显示
const progressFormat = (percentage) => {
  if (totalFiles.value > 0) {
    return `${processedFiles.value}/${totalFiles.value} 文件 - ${progressMessage.value}`;
  }
  return progressMessage.value || `${percentage}%`;
};

// 监听进度更新
const handleProgressUpdate = (percentage, message) => {
  // 确保 percentage 是数字
  const progressValue = typeof percentage === 'number' ? percentage : 0;
  progress.value = progressValue;
  progressMessage.value = message || '';

  // 从消息中提取文件数量信息
  if (message) {
    const match = message.match(/处理中 (\d+)\/(\d+)/);
    if (match) {
      processedFiles.value = parseInt(match[1]);
      totalFiles.value = parseInt(match[2]);
    }
  }

  progressStatus.value = progressValue === 100 ? 'success' : '';
};

// 监听文件夹扫描完成
const handleFolderScanComplete = (results) => {
  progress.value = 100;
  totalFiles.value = results?.length || 0;
  processedFiles.value = results?.length || 0;
  progressMessage.value = `扫描完成，共处理 ${results?.length || 0} 个文件`;
  progressStatus.value = 'success';

  // 如果有错误，显示错误信息
  const errors = results?.filter(r => r.error) || [];
  if (errors.length > 0) {
    ElMessage.warning(`部分文件处理失败: ${errors.length} 个`);
  }
};

// 重置进度状态
const resetProgress = () => {
  progress.value = 0;
  progressStatus.value = '';
  progressMessage.value = '';
  totalFiles.value = 0;
  processedFiles.value = 0;
};

const router = useRouter();

// 打开目录
const openDirectory = async () => {
  try {
    const result = await window.electron.selectDirectory();
    if (result && result.success) {
      currentDirectory.value = result.directory;
      // 重置进度状态
      resetProgress();

      // 获取缩略图路径
      const settingsResult = await window.electron.invoke('load-settings');
      if (!settingsResult.success) {
        ElMessage.error('加载设置失败');
        return;
      }

      // 开始文件夹扫描，只需要缩略图路径
      const scanResult = await window.electron.invoke('start-folder-scan', {
        directoryPath: result.directory,
        settings: {
          thumbnailPath: settingsResult.data.basic.thumbnailPath,
          scanDirectory: result.directory
        }
      });

      if (!scanResult.success) {
        ElMessage.error(scanResult.error || '启动文件夹扫描失败');
      }
    } else if (result && result.error && result.error !== '用户取消了选择') {
      ElMessage.error(result.error);
    }
  } catch (error) {
    console.error('选择文件夹失败:', error);
    ElMessage.error(error.message || '选择文件夹失败');
  }
};

// 选择文件
const handleFileSelect = async (filePath) => {
  console.log("App: File selected:", filePath);
  selectedFile.value = filePath;
  // Reset params when a new file is selected, let ImageWorkspace handle internal reset via loadImage
  Object.assign(editParams, {
    brightness: 0, contrast: 0, saturation: 0, sharpness: 0,
    rotation: 0, crop: null, cropRatio: 'free',
    flipX: false, flipY: false
  });
  /*  // 添加操作到历史记录
    await historyService.addOperation({
      type: 'adjustParams',
      params: editParams,
      moduleId: "default",
      filePath: filePath,
      isOriginalImage: false
    });*/
};

// 参数更新 (来自 ToolPanel 或 ImageWorkspace 内部操作如撤销)
const handleParamsUpdate = (params) => {
  console.log('App: Received params update:', params);
  // Merge new params into existing state
  Object.assign(editParams, params);
  console.log('App: Updated editParams:', {...editParams});
};

// 工具切换 (来自 ToolPanel)
const handleToolChange = (tool) => {
  console.log('App: Tool changed to:', tool);
  if (imageWorkspaceRef.value && imageWorkspaceRef.value.setCurrentTool) {
    imageWorkspaceRef.value.setCurrentTool(tool); // Call child method
  } else {
    console.warn("ImageWorkspace ref not available or setCurrentTool method missing");
  }
};

// 操作应用完成 (来自 ImageWorkspace)
const handleOperationApplied = () => {
  console.log("App: Operation applied, refreshing display");
  refreshImageDisplay();
};

// 刷新图像显示 (通常在应用编辑后调用)
const refreshImageDisplay = async () => {
  const currentFile = selectedFile.value;
  if (currentFile && imageWorkspaceRef.value) {
    console.log("App: Triggering image reload in ImageWorkspace");
    // Instead of resetting selectedFile here, directly call loadImage on child
    try {
      // Ensure loadImage exists on the ref
      if (imageWorkspaceRef.value.loadImage) {
        console.log("11111")
        await imageWorkspaceRef.value.loadImage(currentFile);
      } else {
        console.log("222222222222222")
        console.log(currentFile)
        console.warn("ImageWorkspace ref available but loadImage method missing");
        // Fallback: Reset selectedFile to trigger watch (less ideal)
        // selectedFile.value = '';
        // await nextTick();
        // selectedFile.value = currentFile;
      }
    } catch (error) {
      console.error("Error refreshing image display:", error);
      ElMessage.error("刷新图像显示失败");
    }
  } else {
    console.log("App: Cannot refresh, no file selected or workspace ref missing");
  }
};


/**
 * 修改图片显示 base64
 * @param params
 * @returns {Promise<void>}
 */
const refreshBase64UrlImageDisplay = async (params) => {
  dispalyFile.value = '';
  await nextTick();
  dispalyFile.value = params;
}

// 组件引用
const toolPanelRef = ref(null);
const imageWorkspaceRef = ref(null);

onMounted(() => {
  // 获取应用版本号
  if (window.electron) {
    appVersion.value = window.electron.getAppVersion();
  }

  // 监听进度更新
  if (window.electron) {
    window.electron.onProgress(handleProgressUpdate);
    // 监听文件夹扫描完成
    window.electron.onFolderScanComplete(handleFolderScanComplete);
  }
});

onUnmounted(() => {
  // 清理事件监听
  if (window.electron) {
    window.electron.onProgress(() => {
    });
    window.electron.onFolderScanComplete(() => {
    });
  }
});
</script>

<style>
/* CSS变量：统一定义暗色主题 */
:root,
.dark-theme {
  --bg-color: #1e1e1e; /* 主背景色 - 深灰 */
  --bg-color-light: #252526; /* 面板、侧边栏背景色 - 稍浅的灰 */
  --text-color: #dcdcdc; /* 主要文字颜色 - 浅灰 */
  --text-color-secondary: #8c8c8c; /* 次要文字颜色 - 灰色 */
  --border-color: #3a3a3a; /* 边框颜色 - 深灰 */
  --active-color: #409eff; /* Element Plus 主题蓝 */
  --hover-color: #2c2c2c; /* 鼠标悬停背景色 */
  --panel-color: #252526; /* 与 bg-color-light 一致 */
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  overflow: hidden;
}

#app {
  height: 100vh;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.main-content {
  flex: 1;
  overflow: hidden;
}

.file-explorer {
  border-right: 1px solid var(--border-color);
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-color-light);
  position: relative;
}

.image-workspace {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.tool-panel {
  border-left: 1px solid var(--border-color);
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-color-light);
}

.app-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-color-light);
  border-top: 1px solid var(--border-color);
  color: var(--text-color-secondary);
  font-size: 12px;
  padding: 0 15px;
}

.footer-left, .footer-right {
  display: flex;
  align-items: center;
}

.footer-left p, .footer-right p {
  margin: 0;
}

/* Element Plus 暗色主题适配 */
.dark-theme .el-tree {
  background-color: var(--bg-color-light) !important;
  color: var(--text-color);
}

.dark-theme .el-empty__description,
.dark-theme .el-empty p {
  color: var(--text-color-secondary);
}

.dark-theme .el-button--primary {
  --el-button-bg-color: var(--active-color);
  --el-button-border-color: var(--active-color);
  --el-button-hover-bg-color: color-mix(in srgb, var(--active-color) 90%, #fff);
  --el-button-active-bg-color: color-mix(in srgb, var(--active-color) 80%, #000);
  --el-button-text-color: #ffffff;
}

.dark-theme .el-button {
  --el-button-hover-text-color: var(--active-color);
  --el-button-hover-bg-color: var(--hover-color);
  --el-button-hover-border-color: var(--border-color);
}

.dark-theme .el-button.is-text {
  --el-button-text-color: var(--text-color-secondary);
}

.dark-theme .el-button.is-text:hover {
  --el-button-text-color: var(--active-color);
  background-color: transparent;
}

.dark-theme .el-button--success {
  --el-button-bg-color: #67c23a;
  --el-button-border-color: #67c23a;
  --el-button-text-color: #ffffff;
}

.dark-theme .el-tree-node__content:hover {
  background-color: var(--hover-color);
}

.dark-theme .el-tree-node:focus > .el-tree-node__content {
  background-color: var(--hover-color);
}

.dark-theme .el-slider__bar {
  background-color: var(--active-color);
}

.dark-theme .el-slider__button {
  border-color: var(--active-color);
  background-color: var(--active-color);
}

.dark-theme .el-collapse {
  border-color: var(--border-color);
}

.dark-theme .el-collapse-item__header {
  background-color: transparent;
  color: var(--text-color);
  border-bottom-color: var(--border-color);
}

.dark-theme .el-collapse-item__wrap {
  background-color: transparent;
  border-bottom-color: var(--border-color);
}

.dark-theme .el-collapse-item__content {
  color: var(--text-color);
}

.footer {
  height: 24px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  background-color: var(--bg-color);
  border-top: 1px solid var(--border-color);
}

:deep(.el-progress) {
  width: 100%;
}

:deep(.el-progress-bar__outer) {
  background-color: var(--bg-color-secondary);
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease;
}

:deep(.el-progress__text) {
  color: var(--text-color);
  font-size: 12px;
  min-width: 50px;
  text-align: right;
}
</style> 