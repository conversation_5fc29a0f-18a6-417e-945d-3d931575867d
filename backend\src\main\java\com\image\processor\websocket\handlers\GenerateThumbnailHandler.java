package com.image.processor.websocket.handlers;

import java.util.Map;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.core.ProgressCallback;
import com.image.processor.core.ThumbnailGenerator;
import com.image.processor.websocket.TaskInfo;

/**
 * 生成缩略图处理器
 */
public class GenerateThumbnailHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public GenerateThumbnailHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "generate_thumbnail";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        // 提取参数
        String inputPath = request.getString("inputPath");
        String outputPath = request.getString("outputPath");
        int width = request.optInt("width", 300);
        int height = request.optInt("height", 300);
        int quality = request.optInt("quality", 85);
        boolean keepAspectRatio = request.optBoolean("keepAspectRatio", true);
        String resizeMode = request.optString("resizeMode", "fit");
        
        // 创建任务ID (如果客户端未提供)
        String taskId = request.optString("taskId", createTaskId());
        
        logger.info("收到生成缩略图请求: taskId={}, input={}, output={}, 尺寸={}x{}, quality={}, keepAspectRatio={}, resizeMode={}", 
                   taskId, inputPath, outputPath, width, height, quality, keepAspectRatio, resizeMode);
        
        // 发送任务接收确认
        sendTaskUpdate(conn, taskId, "accepted", 0, "任务已接受，准备处理");
        
        // 创建并存储任务信息
        TaskInfo taskInfo = new TaskInfo(taskId, conn);
        activeTasks.put(taskId, taskInfo);
        
        // 提交任务到线程池
        taskExecutor.submit(() -> {
            try {
                // 如果任务已被取消，直接返回
                if (taskInfo.isCancelled()) {
                    logger.info("任务已被取消，不执行: {}", taskId);
                    return;
                }
                
                // 任务开始
                sendTaskUpdate(conn, taskId, "started", 0, "开始处理");
                
                // 创建缩略图生成器
                ThumbnailGenerator generator = new ThumbnailGenerator();
                
                // 创建进度回调
                final ProgressCallback progressCallback = new ProgressCallback() {
                    @Override
                    public boolean onProgress(int progress, String message) {
                        if (taskInfo.isCancelled()) {
                            return false; // 返回false表示任务已取消，应停止处理
                        }
                        sendTaskUpdate(conn, taskId, "progress", progress, message);
                        return true; // 返回true表示继续处理
                    }
                };
                
                // 执行缩略图生成
                boolean success = generator.generateThumbnail(
                    inputPath, outputPath, width, height, 
                    quality, keepAspectRatio, resizeMode, 
                    progressCallback
                );
                
                // 任务已被取消
                if (taskInfo.isCancelled()) {
                    logger.info("任务在处理过程中被取消: {}", taskId);
                    sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
                    return;
                }
                
                // 根据结果发送消息
                if (success) {
                    JSONObject resultData = new JSONObject();
                    resultData.put("thumbnailPath", outputPath);
                    sendTaskUpdate(conn, taskId, "completed", 100, resultData);
                    logger.info("缩略图生成成功: taskId={}, output={}", taskId, outputPath);
                } else {
                    sendTaskUpdate(conn, taskId, "failed", 0, "缩略图生成失败");
                    logger.error("缩略图生成失败: taskId={}", taskId);
                }
            } catch (Exception e) {
                logger.error("执行任务时出错: taskId=" + taskId, e);
                sendTaskUpdate(conn, taskId, "error", 0, "处理出错: " + e.getMessage());
            } finally {
                // 移除活动任务
                activeTasks.remove(taskId);
            }
        });
    }
} 
 