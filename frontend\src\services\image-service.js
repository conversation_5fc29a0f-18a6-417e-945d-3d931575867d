/**
 * 图像处理服务
 * 负责图像的处理和缓存
 */

import path from 'node:path';
import {BrowserWindow} from 'electron';
import {
    connectToWebSocket,
    generateTaskId,
    setTaskCallback,
    isConnected,
    sendWebSocketMessage,
    removeTaskCallback, getTaskCallback
} from './websocket-service.js';
import TaskIdUtils from '../utils/TaskIdUtils.js';
import {store} from '../config/store-config.js';

// 主窗口引用
let mainWindow = null;

/**
 * 设置主窗口引用
 * @param {Electron.BrowserWindow} window 主窗口对象
 */
export function setMainWindow(window) {
    mainWindow = window;
}

/**
 * 发送进度更新
 * @param {number} progress 进度值（0-100）
 * @param {string} operation 操作描述
 */
export function sendProgress(progress, operation) {
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('ipc-progress', progress, operation);
    }
}

/**
 * 发送预览更新
 * @param {string} taskId 任务ID
 * @param {boolean} success 是否成功
 * @param {string} outputPath 输出路径
 * @param {string} error 错误信息
 */
function sendPreviewUpdate(taskId, success, outputPath, error) {
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('preview-update', {
            taskId,
            success,
            outputPath,
            error
        });
    }
}

/**
 * 处理图像编辑预览
 * @param {Object} data 预览数据
 * @returns {Promise<Object>} 处理结果
 */
export async function previewImageEdit(data) {
    console.log('[Image Service] Received preview-image-edit request.');

    const {originalPath, thumbnailPath, params, changedParamsOnly, sessionId, useBaseline} = data;

    if (!originalPath || !thumbnailPath) {
        return {success: false, error: '参数不完整，需要原图路径和缩略图路径'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        await connectToWebSocket();
        if (!isConnected()) {
            return {success: false, error: '无法连接到图像处理服务'};
        }
    }

    // 使用TaskIdUtils工具类基于原图路径生成标准化的任务ID
    const normalizedOriginalPath = TaskIdUtils.normalizePath(originalPath);
    const taskId = TaskIdUtils.createTaskId(normalizedOriginalPath);

    console.log(`[Image Service] Generated Standardized Task ID from original: ${taskId}`);
    console.log(`[Image Service] OriginalPath: ${normalizedOriginalPath}`);
    console.log(`[Image Service] ThumbnailPath: ${thumbnailPath}`);

    // 准备 WebSocket 请求
    const request = {
        action: 'adjust_params',
        taskId: taskId,
        originalPath: normalizedOriginalPath,  // 发送原图路径
        inputPath: thumbnailPath,              // 实际处理的缩略图路径
        params: params
    };

    // 添加会话ID（如果提供）
    if (sessionId) {
        request.sessionId = sessionId;
    }

    // 添加是否使用基准图标志（如果提供）
    if (useBaseline !== undefined) {
        request.useBaseline = useBaseline;
    }

    // 注册回调，用于处理结果并发送回渲染进程
    setTaskCallback(taskId, {
        resolve: (resultData) => {
            console.log(`任务 ${taskId} (preview) 完成，准备发送 preview-update`);
            sendPreviewUpdate(taskId, true, resultData?.outputPath);
            removeTaskCallback(taskId); // 处理完后删除回调
        },
        reject: (error) => {
            console.error(`任务 ${taskId} (preview) 失败，准备发送 preview-update`);
            console.log(`[Main Process - Callback] Sending preview-update (failure) for taskId: ${taskId}`);
            sendPreviewUpdate(taskId, false, null, error instanceof Error ? error.message : String(error));
            removeTaskCallback(taskId); // 处理完后删除回调
        }
    });

    // 发送 WebSocket 请求
    try {
        sendWebSocketMessage(request);
        console.log(`[Image Service] Sent adjust_params request to WebSocket for taskId: ${taskId}`);
        return {success: true, taskId: taskId, message: '预览请求已发送'};
    } catch (error) {
        console.error('[Image Service] Error sending WebSocket request:', error);
        removeTaskCallback(taskId);
        return {success: false, error: '发送预览请求失败: ' + error.message};
    }
}

/**
 * 获取缓存图像
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 图像数据
 */
export async function getCachedImage(data) {
    console.log('[Image Service] Received get-cached-image request.');

    const {taskId, quality = 85} = data;

    if (!taskId) {
        console.error('[Image Service] Error: Missing taskId parameter.');
        return {success: false, error: 'Missing taskId parameter'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到图像处理服务'};
        }
    }

    // 创建一个Promise来等待WebSocket响应
    return new Promise((resolve, reject) => {
        // 设置超时
        const timeoutId = setTimeout(() => {
            removeTaskCallback(taskId);
            reject({success: false, error: '获取图像超时'});
        }, 10000); // 10秒超时

        // 直接设置回调函数，而不是包装在对象中
        setTaskCallback(taskId, (response) => {
            clearTimeout(timeoutId);

            if (response.status === 'image_data') {
                resolve({
                    success: true,
                    taskId: taskId,
                    format: response.data.format,
                    imageData: response.data.imageData
                });
            } else if (['error', 'failed'].includes(response.status)) {
                reject({success: false, error: response.data});
            }
        });

        // 发送WebSocket请求
        try {
            const request = {
                action: 'get_cached_image',
                taskId: taskId,
                quality: quality
            };

            sendWebSocketMessage(request);
            console.log(`[Image Service] Sent get_cached_image request for taskId: ${taskId}`);
        } catch (error) {
            clearTimeout(timeoutId);
            removeTaskCallback(taskId);
            reject({success: false, error: `发送请求失败: ${error.message}`});
        }
    });
}

/**
 * 获取缓存图像基于参数
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 图像数据
 */
export async function getCachedImageApplyParams(data) {
    console.log('[Image Service] Received get-cached-image-apply-params request.');

    const {taskId, quality = 85} = data;

    if (!taskId) {
        console.error('[Image Service] Error: Missing taskId parameter.');
        return {success: false, error: 'Missing taskId parameter'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到图像处理服务'};
        }
    }

    // 创建一个Promise来等待WebSocket响应
    return new Promise((resolve, reject) => {
        // 设置超时
        const timeoutId = setTimeout(() => {
            removeTaskCallback(taskId);
            reject({success: false, error: '获取图像超时'});
        }, 10000); // 10秒超时

        // 直接设置回调函数，而不是包装在对象中
        setTaskCallback(taskId, (response) => {
            clearTimeout(timeoutId);

            if (response.status === 'image_data') {
                resolve({
                    success: true,
                    taskId: taskId,
                    format: response.data.format,
                    imageData: response.data.imageData
                });
            } else if (['error', 'failed'].includes(response.status)) {
                reject({success: false, error: response.data});
            }
        });

        // 发送WebSocket请求
        try {
            const request = {
                action: 'get_cached_image_apply_params',
                taskId: taskId,
                quality: quality,
                data:data
            };
            sendWebSocketMessage(request);
            console.log(`[Image Service] Sent get_cached_image request for taskId: ${taskId}`);
        } catch (error) {
            clearTimeout(timeoutId);
            removeTaskCallback(taskId);
            reject({success: false, error: `发送请求失败: ${error.message}`});
        }
    });
}

/**
 * 处理文件夹扫描
 * @param {string|Object} data 扫描数据
 * @returns {Promise<Object>} 处理结果
 */
export async function startFolderScan(data) {
    console.log('start-folder-scan接收到的参数:', data);

    // 支持直接传入路径字符串
    let directoryPath, settings;
    if (typeof data === 'string') {
        directoryPath = data;
        settings = store.get('basic'); // 使用默认设置
        console.log('收到路径字符串参数:', directoryPath);
    } else {
        // 原有的对象参数处理
        directoryPath = data.directoryPath;
        settings = data.settings;
        console.log('directoryPath:', directoryPath);
        console.log('directoryPath类型:', typeof directoryPath);
    }

    if (!directoryPath) {
        return {success: false, error: '请选择有效的扫描目录'};
    }

    try {
        // 发送进度更新
        sendProgress(0, `开始处理文件夹 ${directoryPath}`);

        // 确保WebSocket已连接
        if (!isConnected()) {
            await connectToWebSocket();
            if (!isConnected()) {
                return {success: false, error: '无法连接到图像处理服务'};
            }
        }

        return new Promise((resolve, reject) => {
            const taskId = generateTaskId();

            // 存储任务回调
            setTaskCallback(taskId, {
                resolve: (data) => {
                    resolve({
                        success: true,
                        data: data.results,
                        message: `处理完成，共 ${data.results.length} 个文件`
                    });
                },
                reject: (error) => {
                    reject({success: false, error: error.message});
                },
                onProgress: (progress, message) => {
                    sendProgress(progress, message);
                }
            });

            // 发送处理文件夹请求
            const request = {
                action: 'process_directory',
                taskId: taskId,
                directoryPath: directoryPath,
                thumbnailDir: settings.thumbnailPath,
                scanDirectory: settings.scanDirectory
            };

            sendWebSocketMessage(request);
        });
    } catch (error) {
        console.error('文件夹扫描失败:', error);
        return {success: false, error: error.message};
    }
}

/**
 * 设置基准图
 * @param {Object} data 请求数据  save_baseline
 * @returns {Promise<Object>} 图像数据
 */
export async function setBaselineImage(data) {

    console.log('[Image Service] Received setBaselineImage request.');

    const {taskId, quality = 85} = data;
    console.log(taskId)
    console.log(taskId)
    console.log(taskId)
    console.log(taskId)

    if (!taskId) {
        console.error('[Image Service] Error: Missing taskId parameter.');
        return {success: false, error: 'Missing taskId parameter'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到设置基准图服务'};
        }
    }

    // 创建Promise以等待缩略图生成完成
    return new Promise((resolve) => {
        //  const taskId = generateTaskId();
        console.log(taskId)
        console.log(taskId)
        setTaskCallback(taskId, {
            resolve: (res) => {
                resolve({
                    success: true,
                    resultData: res
                });

            },
            reject: (error) => {
                resolve({success: false, error: error.message});
            }
        });

        console.log(data.taskId)
        console.log(data.taskId)
        console.log(data.taskId)
        // 发送WebSocket请求
        const request = {
            action: 'save_baseline',
            taskId: data.taskId,
            requestId: data.taskId,
        };

        sendWebSocketMessage(request);
    });
}


/**
 * 获取缓存图像
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 图像数据
 */
export async function sendLoadImage(data) {

    console.log('[Image Service] Received get-cached-image request.');

    const {taskId, quality = 85} = data;

    if (!taskId) {
        console.error('[Image Service] Error: Missing taskId parameter.');
        return {success: false, error: 'Missing taskId parameter'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到图像处理服务'};
        }
    }

    return new Promise((resolve) => {
        //  const taskId = generateTaskId();
        setTaskCallback(taskId, {
            resolve: (res) => {
                let imagePath = res.imagePath;
                if (imagePath) {
                    resolve({
                        success: true,
                        resultData: res,
                        imagePath: imagePath,
                    });
                }

            },
            reject: (error) => {
                resolve({success: false, error: error.message});
            }
        });

        // 发送WebSocket请求
        const request = {
            action: 'handle_image_click',
            originalPath: data.originalPath,
            thumbnailPath: data.thumbnailPath,
            taskId: taskId,
            moduleId: data.currentModuleId,
            currentModuleParams: data.currentModuleParams,
            otherModuleParams: data.otherModuleParams
        };

        sendWebSocketMessage(request);
    });

}


/**
 * 设置基础参数
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 图像数据
 */
export async function sendBasicAdjustParams(data) {

    console.log('[Image Service] Received get-cached-image request.');

    const {taskId, quality = 85} = data;

    if (!taskId) {
        console.error('[Image Service] Error: Missing taskId parameter.');
        return {success: false, error: 'Missing taskId parameter'};
    }

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到设置基础参数服务'};
        }
    }

    return new Promise((resolve) => {
        //  const taskId = generateTaskId();
        setTaskCallback(taskId, {
            resolve: (res) => {
                resolve({
                    success: true,
                    resultData: res
                });

            },
            reject: (error) => {
                resolve({success: false, error: error.message});
            }
        });
        // 发送WebSocket请求
        const request = {
            action: 'adjust_params',
            inputPath: data.inputPath,
            //   outputPath: data.inputPath,
            taskId: data.taskId,
            currentBaselineImage: data.currentBaselineImage,
            originalPath: data.originalPath,
            params: data.params,
            //  isFullParams: true,
        };

        sendWebSocketMessage(request);
    });

}


/**
 * 应用图像修改
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 图像数据
 */
export async function sendApplyImageEdit(data) {

    console.log('[Image Service] Received get-cached-image request.');

    /* const {taskId, quality = 85} = data;

     if (!taskId) {
       console.error('[Image Service] Error: Missing taskId parameter.');
       return {success: false, error: 'Missing taskId parameter'};
     }
   */
    const taskId = TaskIdUtils.createTaskId(data.filePath);

    // 确保 WebSocket 连接
    if (!isConnected()) {
        console.log('[Image Service] WebSocket not connected, attempting to connect...');
        await connectToWebSocket();
        if (!isConnected()) {
            console.error('[Image Service] Error: Failed to connect to WebSocket service.');
            return {success: false, error: '无法连接到设置基础参数服务'};
        }
    }

    return new Promise((resolve) => {
        //  const taskId = generateTaskId();
        setTaskCallback(taskId, {
            resolve: (res) => {
                resolve({
                    success: true,
                    resultData: res
                });

            },
            reject: (error) => {
                resolve({success: false, error: error.message});
            }
        });


        console.log("111111111111")
        console.log(data)
        console.log(data.operation)
        console.log(data.operation)

        let actionName = "";
        if (data.operation === "crop") {
            actionName = 'crop_image'
        }else if (data.operation === "rotate") {
            actionName = 'rotate_image'
        }
        // 发送WebSocket请求
        const request = {
            taskId: taskId,
            action: actionName,
            inputPath: data.filePath,
            baselineImagePath: data.baselineImagePath,
            params: data.params, //  params: { crop: { x: 130, y: 61, width: 1039, height: 488 } }
        };

        sendWebSocketMessage(request);
    });
}
