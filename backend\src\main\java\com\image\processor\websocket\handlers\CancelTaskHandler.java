package com.image.processor.websocket.handlers;

import java.util.Map;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;

import com.image.processor.websocket.TaskInfo;

/**
 * 取消任务请求处理器
 */
public class CancelTaskHandler extends BaseRequestHandler {
    private final Map<String, TaskInfo> activeTasks;
    
    public CancelTaskHandler(ExecutorService taskExecutor, Map<String, TaskInfo> activeTasks) {
        super(taskExecutor);
        this.activeTasks = activeTasks;
    }

    @Override
    public String getActionName() {
        return "cancel_task";
    }

    @Override
    public void handle(WebSocket conn, JSONObject request) throws JSONException {
        String taskId = request.getString("taskId");
        TaskInfo taskInfo = activeTasks.get(taskId);
        
        if (taskInfo != null) {
            taskInfo.cancel();
            sendTaskUpdate(conn, taskId, "cancelled", 0, "任务已取消");
            logger.info("任务取消请求: taskId={}", taskId);
        } else {
            sendError(conn, "找不到任务: " + taskId);
        }
    }
} 
 
 