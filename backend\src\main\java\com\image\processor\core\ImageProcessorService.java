package com.image.processor.core;

import com.image.processor.utils.BaselineImageManager;
import org.bytedeco.javacpp.indexer.DoubleRawIndexer;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.bytedeco.opencv.global.opencv_core.*;
import static org.bytedeco.opencv.global.opencv_imgcodecs.imwrite;
import static org.bytedeco.opencv.global.opencv_imgproc.*;

/**
 * 图像处理服务
 * 提供各种图像处理功能，并利用缓存系统提高性能
 */
public class ImageProcessorService {
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessorService.class);
    
    // 缓存系统
    private final ImageProcessingCache cache;
    
    // 基准图管理器
    private final BaselineImageManager baselineManager;
    
    // 单例实例
    private static ImageProcessorService instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized ImageProcessorService getInstance() {
        if (instance == null) {
            instance = new ImageProcessorService();
        }
        return instance;
    }
    
    private ImageProcessorService() {
        this.cache = ImageProcessingCache.getInstance();
        this.baselineManager = BaselineImageManager.getInstance();
        logger.info("图像处理服务已初始化");
    }
    
    /**
     * 应用基础调整到图像（使用缓存）
     *
     * @param inputPath 输入图像路径
     * @param params 调整参数
     * @param callback 进度回调
     * @return 处理结果，包含缓存的 taskId 和处理后的 Mat 对象
     */
    public ProcessingResult applyBasicAdjustments(String inputPath, BasicAdjustment.AdjustmentParams params, ProgressCallback callback) {
        if (inputPath == null || params == null) {
            logger.error("输入参数无效");
            if (callback != null) callback.onProgress(0, "错误: 输入参数无效");
            return null;
        }
        
        // 报告开始处理
        if (callback != null && !callback.onProgress(0, "开始处理图像调整")) {
            return null;
        }
        
        // 设置当前活跃图像
        cache.setActiveImagePath(inputPath);
        
        // 创建任务ID - 仅基于图像路径，不包含处理参数
        // 注意: 此处使用输入路径生成taskId，应确保输入路径是原图路径而非缩略图路径
        String taskId = cache.createTaskId(inputPath);
        
        logger.info("处理任务ID(仅基于图像路径): {}, 参数: 亮度={}, 对比度={}, 饱和度={}, 锐化={}",
                taskId, params.getBrightness(), params.getContrast(), params.getSaturation(), params.getSharpness());
        
        // 检查缓存
        if (cache.hasCache(taskId)) {
            Mat result = cache.getFromCache(taskId);
            if (result != null) {
                logger.info("使用缓存的处理结果: {}", taskId);
                if (callback != null) callback.onProgress(100, "使用缓存图像");
                return new ProcessingResult(taskId, result);
            }
        }
        
        // 加载图像
        if (callback != null && !callback.onProgress(10, "正在读取图像...")) {
            return null;
        }
        
        Mat image = cache.getOrLoadSourceImage(inputPath);
        if (image == null) {
            logger.error("无法读取图像: {}", inputPath);
            if (callback != null) callback.onProgress(0, "错误: 无法读取图像");
            return null;
        }
        
        try {
            // 转换到RGB色彩空间以方便处理
            if (callback != null && !callback.onProgress(20, "正在处理色彩空间...")) {
                image.release();
                return null;
            }
            
            Mat rgbImage = new Mat();
            cvtColor(image, rgbImage, COLOR_BGR2RGB);
            
            // 调整亮度和对比度
            if (callback != null && !callback.onProgress(30, "正在调整亮度和对比度...")) {
                image.release();
                rgbImage.release();
                return null;
            }
            
            Mat adjustedImage = adjustBrightnessContrast(rgbImage, params.getBrightness(), params.getContrast());
            rgbImage.release();
            
            // 调整饱和度
            if (callback != null && !callback.onProgress(50, "正在调整饱和度...")) {
                image.release();
                adjustedImage.release();
                return null;
            }
            
            Mat afterSaturation = adjustSaturation(adjustedImage, params.getSaturation());
            adjustedImage.release();
            
            // 应用锐化
            if (callback != null && !callback.onProgress(70, "正在应用锐化...")) {
                image.release();
                afterSaturation.release();
                return null;
            }
            
            Mat result = afterSaturation;
            if (params.getSharpness() > 0) {
                result = applySharpening(afterSaturation, params.getSharpness());
                afterSaturation.release();
            }
            
            // 转换回BGR色彩空间用于保存
            if (callback != null && !callback.onProgress(85, "正在完成处理...")) {
                image.release();
                result.release();
                return null;
            }
            
            Mat outputImage = new Mat();
            cvtColor(result, outputImage, COLOR_RGB2BGR);
            result.release();
            
            // 存入缓存
            cache.putToCache(taskId, outputImage);
            
            if (callback != null) {
                callback.onProgress(100, "图像调整完成");
            }
            
            logger.info("图像调整完成，已缓存: {}", taskId);
            return new ProcessingResult(taskId, outputImage.clone());
            
        } catch (Exception e) {
            logger.error("处理图像时出错", e);
            if (callback != null) {
                callback.onProgress(0, "错误: " + e.getMessage());
            }
            return null;
        } finally {
            // 释放原始图像资源
            if (image != null) {
                image.release();
            }
        }
    }
    
    /**
     * 应用基础调整到图像（使用缓存，并使用原图路径生成TaskID）
     *
     * @param thumbnailPath 缩略图路径（用于处理）
     * @param originalPath 原图路径（用于生成TaskID）
     * @param params 调整参数
     * @param callback 进度回调
     * @return 处理结果，包含缓存的 taskId 和处理后的 Mat 对象
     */
    public ProcessingResult applyBasicAdjustmentsWithOriginal(String thumbnailPath, String originalPath, BasicAdjustment.AdjustmentParams params, ProgressCallback callback) {
        if (thumbnailPath == null || originalPath == null || params == null) {
            logger.error("输入参数无效");
            if (callback != null) callback.onProgress(0, "错误: 输入参数无效");
            return null;
        }
        
        // 报告开始处理
        if (callback != null && !callback.onProgress(0, "开始处理图像调整")) {
            return null;
        }
        
        // 切换到新图像，清理旧的缓存
        if (!originalPath.equals(cache.getActiveImagePath())) {
            logger.info("切换到新图像: {}", originalPath);
            cache.onImageSwitch();
        }
        
        // 设置当前活跃图像（设置原图和缩略图都为活跃）
        cache.setActiveImagePath(originalPath);
        
        // 使用原图路径创建任务ID - 只基于原图路径，不包含参数
        // 注意: 此处明确使用原图路径生成taskId，这是正确的做法
        String taskId = cache.createTaskId(originalPath);
        
        logger.info("处理任务ID(仅基于原图): {}, 原图路径: {}, 缩略图路径: {}, 参数: 亮度={}, 对比度={}, 饱和度={}, 锐化={}",
                taskId, originalPath, thumbnailPath, params.getBrightness(), params.getContrast(), params.getSaturation(), params.getSharpness());

        // 检查缓存
    /*    if (cache.hasCache(taskId)) {
            Mat result = cache.getFromCache(taskId);
            if (result != null) {
                logger.info("使用缓存的处理结果: {}", taskId);
                if (callback != null) callback.onProgress(100, "使用缓存图像");
                return new ProcessingResult(taskId, result);
            }
        }*/
        logger.info((callback != null)+"");
     //   logger.info((!callback.onProgress(10, "正在读取图像..."))+"");
        // 加载缩略图
        if (callback != null && !callback.onProgress(10, "正在读取图像...")) {
            return null;
        }
        
        Mat image = cache.getOrLoadSourceImage(thumbnailPath);
        if (image == null) {
            logger.error("无法读取缩略图: {}", thumbnailPath);
            if (callback != null) callback.onProgress(0, "错误: 无法读取缩略图");
            return null;
        }
        
        try {
            // 转换到RGB色彩空间以方便处理
            if (callback != null && !callback.onProgress(20, "正在处理色彩空间...")) {
                image.release();
                return null;
            }
            
            Mat rgbImage = new Mat();
            cvtColor(image, rgbImage, COLOR_BGR2RGB);
            
            // 调整亮度和对比度
            if (callback != null && !callback.onProgress(30, "正在调整亮度和对比度...")) {
                image.release();
                rgbImage.release();
                return null;
            }
            logger.info("adjustedImage");
            Mat adjustedImage = adjustBrightnessContrast(rgbImage, params.getBrightness(), params.getContrast());
            rgbImage.release();
            
            // 调整饱和度
            if (callback != null && !callback.onProgress(50, "正在调整饱和度...")) {
                image.release();
                adjustedImage.release();
                return null;
            }
            
            Mat afterSaturation = adjustSaturation(adjustedImage, params.getSaturation());
            adjustedImage.release();
            
            // 应用锐化
            if (callback != null && !callback.onProgress(70, "正在应用锐化...")) {
                image.release();
                afterSaturation.release();
                return null;
            }
            
            Mat result = afterSaturation;
            if (params.getSharpness() > 0) {
                result = applySharpening(afterSaturation, params.getSharpness());
                afterSaturation.release();
            }
            
            // 转换回BGR色彩空间用于保存
            if (callback != null && !callback.onProgress(85, "正在完成处理...")) {
                image.release();
                result.release();
                return null;
            }
            
            Mat outputImage = new Mat();
            cvtColor(result, outputImage, COLOR_RGB2BGR);
      /*      boolean saved = imwrite(thumbnailPath, outputImage);
            if (!saved) {
                logger.error("保存图像失败: {}", thumbnailPath);
                return null;
            }

            logger.info("已保存处理后的图像: {}", thumbnailPath);
            result.release();*/
            
            // 存入缓存（使用基于原图的taskId）
            cache.putToCache(taskId, outputImage);
            
            if (callback != null) {
                callback.onProgress(100, "图像调整完成");
            }
            
            logger.info("图像调整完成，已缓存: {}", taskId);
            return new ProcessingResult(taskId, outputImage.clone());
            
        } catch (Exception e) {
            logger.error("处理图像时出错", e);
            if (callback != null) {
                callback.onProgress(0, "错误: " + e.getMessage());
            }
            return null;
        } finally {
            // 释放原始图像资源
            if (image != null) {
                image.release();
            }
        }
    }
    
    /**
     * 将处理后的图像保存到文件
     * 
     * @param result 处理结果
     * @param outputPath 输出路径（如果为null则创建临时文件）
     * @return 输出文件路径，失败返回null
     */
    public String saveProcessedImage(ProcessingResult result, String outputPath) {
        if (result == null || result.image == null || result.image.empty()) {
            logger.error("无效的处理结果");
            return null;
        }
        
        // 如果未指定输出路径，则创建临时文件
        if (outputPath == null || outputPath.trim().isEmpty()) {
            try {
                Path tempFile = Files.createTempFile("img_proc_", ".jpg");
                outputPath = tempFile.toString();
                logger.info("未指定输出路径，创建临时文件: {}", outputPath);
            } catch (IOException e) {
                logger.error("创建临时文件失败", e);
                return null;
            }
        }
        
        // 创建输出目录（如果不存在）
        File outputFile = new File(outputPath);
        File outputDir = outputFile.getParentFile();
        if (outputDir != null && !outputDir.exists()) {
            if (!outputDir.mkdirs()) {
                logger.warn("无法创建输出目录: {}", outputDir.getAbsolutePath());
            }
        }
        
        // 保存图像
        boolean saved = imwrite(outputPath, result.image);
        if (!saved) {
            logger.error("保存图像失败: {}", outputPath);
            return null;
        }
        
        logger.info("已保存处理后的图像: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 基准图不存在异常
     */
    public static class NoBaselineImageException extends Exception {
        public NoBaselineImageException(String message) {
            super(message);
        }
    }

    /**
     * 直接从缓存获取处理后的图像字节数组
     * 
     * @param taskId 任务ID
     * @param quality JPEG质量（0-100）
     * @return JPEG格式的字节数组
     * @throws NoBaselineImageException 如果找不到缓存且没有可用的基准图
     */
    public byte[] getCachedImageBytes(String taskId, int quality) throws NoBaselineImageException {
        // 使用前端传入的taskId直接查询缓存
        logger.info("获取缓存图像: taskId={}", taskId);
        
        Mat image = cache.getFromCache(taskId);
        if (image == null) {
            logger.warn("缓存中不存在直接匹配的任务: {}, 尝试查找相关的基准图", taskId);
            
            // 尝试从会话对应的基准图获取图像
            boolean foundBaselineForTaskId = false;
            for (String baselinePath : baselineManager.getAllBaselineImages()) {
                String baselineTaskId = baselineManager.getBaselineTaskId(baselinePath);
                if (baselineTaskId != null && baselineTaskId.equals(taskId)) {
                    // 找到匹配的基准图，从文件加载
                    foundBaselineForTaskId = true;
                    logger.info("找到与taskId={}关联的基准图: {}", taskId, baselinePath);
                    image = cache.loadImage(baselinePath);
                    break;
                }
            }
            
            if (image == null) {
                if (foundBaselineForTaskId) {
                    // 找到了关联的基准图，但无法加载
                    logger.error("无法加载与taskId={}关联的基准图", taskId);
                    throw new NoBaselineImageException("无法加载关联的基准图");
                } else {
                    // 完全找不到相关的基准图
                    logger.error("无法找到任何与taskId={}相关的基准图", taskId);
                    throw new NoBaselineImageException("找不到关联的基准图");
                }
            }
        }
        
        byte[] data = cache.matToJpegBytes(image, quality);
        image.release(); // 释放克隆的Mat
        return data;
    }
    
    /**
     * 调整亮度和对比度
     */
    private Mat adjustBrightnessContrast(Mat image, double brightness, double contrast) {
        logger.info("adjustBrightnessContrast");
        Mat result = new Mat();
        image.convertTo(result, -1, contrast, brightness);
        return result;
    }
    
    /**
     * 调整饱和度
     */
    private Mat adjustSaturation(Mat rgbImage, double saturation) {
        if (Math.abs(saturation - 1.0) < 0.01) {
            return rgbImage.clone();
        }
        
        // 转换为HSV色彩空间
        Mat hsvImage = new Mat();
        cvtColor(rgbImage, hsvImage, COLOR_RGB2HSV);
        
        // 分离通道
        MatVector channels = new MatVector(3);
        split(hsvImage, channels);
        
        // 调整饱和度通道
        Mat saturationChannel = channels.get(1);
        Mat scaledChannel = new Mat();
        
        // 创建与factor相同值的Mat对象用于乘法操作
        Mat factorMat = new Mat(saturationChannel.rows(), saturationChannel.cols(), saturationChannel.type(), Scalar.all(saturation));
        multiply(saturationChannel, factorMat, scaledChannel);
        
        // 确保值在[0, 255]范围内
        threshold(scaledChannel, scaledChannel, 255, 255, THRESH_TRUNC);
        threshold(scaledChannel, scaledChannel, 0, 0, THRESH_TOZERO);
        
        // 替换原始饱和度通道
        channels.put(1, scaledChannel);
        
        // 合并通道
        merge(channels, hsvImage);
        
        // 转换回RGB
        Mat result = new Mat();
        cvtColor(hsvImage, result, COLOR_HSV2RGB);
        
        // 释放资源
        hsvImage.release();
        for (int i = 0; i < channels.size(); i++) {
            channels.get(i).release();
        }
        scaledChannel.release();
        factorMat.release();
        
        return result;
    }
    
    /**
     * 应用锐化效果
     */
    private Mat applySharpening(Mat image, double sharpness) {
        if (sharpness <= 0) {
            return image.clone();
        }
        
        // 使用OpenCV的拉普拉斯算子进行锐化
        Mat blurred = new Mat();
        Mat laplacian = new Mat();
        Mat sharpened = new Mat();
        
        // 先用高斯模糊减少噪声
        GaussianBlur(image, blurred, new Size(3, 3), 0);
        
        // 应用拉普拉斯算子检测边缘
        Laplacian(blurred, laplacian, CV_16S, 3, 1, 0, BORDER_DEFAULT);
        
        // 转换拉普拉斯结果到合适的格式
        Mat laplacianAbs = new Mat();
        convertScaleAbs(laplacian, laplacianAbs);
        
        // 根据锐化强度，将原图像与拉普拉斯结果混合
        addWeighted(image, 1.0, laplacianAbs, sharpness, 0, sharpened);
        
        // 释放资源
        blurred.release();
        laplacian.release();
        laplacianAbs.release();
        
        return sharpened;
    }
    
    /**
     * 关闭服务并释放资源
     */
    public void shutdown() {
        cache.shutdown();
        logger.info("图像处理服务已关闭");
    }
    
    /**
     * 处理结果类
     */
    public static class ProcessingResult {
        public final String taskId;
        public final Mat image;
        public String thumbnailPath;
        
        public ProcessingResult(String taskId, Mat image) {
            this.taskId = taskId;
            this.image = image;
        }
        
        public void release() {
            if (image != null) {
                image.release();
            }
        }
    }
    
    /**
     * 根据TaskID获取处理结果
     * 
     * @param taskId 任务ID
     * @return 处理结果，不存在返回null
     */
    public ProcessingResult getProcessingResultFromTaskId(String taskId) {
        if (taskId == null || !cache.hasCache(taskId)) {
            return null;
        }
        
        Mat image = cache.getFromCache(taskId);
        if (image == null) {
            return null;
        }
        
        return new ProcessingResult(taskId, image);
    }
    
    /**
     * 保存当前处理结果为基准图
     * 注意: 已简化设计，移除sessionId，仅使用taskId作为基准图标识
     * 
     * @param taskId 当前处理结果的TaskID
     * @return 基准图路径，失败返回null
     */
    public String saveAsBaseline(String taskId) {
        // 从缓存获取图像
        ProcessingResult result = getProcessingResultFromTaskId(taskId);
        if (result == null) {
            logger.error("无法获取TaskID对应的处理结果: {}", taskId);
            return null;
        }
        
        try {
            // 创建基准图路径
            String baselineImagePath = createBaselineImagePath(taskId);
            
            // 保存图像
            String savedPath = saveProcessedImage(result, baselineImagePath);
            if (savedPath != null) {
                // 记录基准图信息
                baselineManager.setBaselineImage(taskId, savedPath);
                
                // 设置为基准图任务ID，确保不会被缓存清理
                cache.setBaselineTaskId(taskId);
                
                logger.info("已保存基准图: {}", savedPath);
                return savedPath;
            } else {
                logger.error("保存基准图失败");
                return null;
            }
        } finally {
            // 释放资源
            result.release();
        }
    }
    
    /**
     * 创建基准图路径
     * 
     * @param baselineTaskId 基准图任务ID
     * @return 基准图路径
     */
    private String createBaselineImagePath(String baselineTaskId) {
        try {
            // 创建临时文件路径，使用任务ID确保唯一性
            java.nio.file.Path tempDir = java.nio.file.Files.createTempDirectory("baseline_" + baselineTaskId.substring(0, 8));
            String fileName = "baseline_" + System.currentTimeMillis() + ".jpg";
            return tempDir.resolve(fileName).toString();
        } catch (Exception e) {
            logger.error("创建基准图路径失败", e);
            // 备用方案
            return System.getProperty("java.io.tmpdir") + "/baseline_" + baselineTaskId + "_" + System.currentTimeMillis() + ".jpg";
        }
    }
    
    /**
     * 应用基础调整到图像（使用基准图）
     * 注意: 已简化设计，移除sessionId，仅使用baselineTaskId作为基准图标识
     *
     * @param baselineTaskId 基准图任务ID
     * @param inputPath 输入图像路径（如果没有基准图，则使用此路径）
     * @param params 调整参数
     * @param callback 进度回调
     * @return 处理结果，包含缓存的 taskId 和处理后的 Mat 对象
     */
    public ProcessingResult applyAdjustmentsWithBaseline(String baselineTaskId, String inputPath, 
                                                        BasicAdjustment.AdjustmentParams params, 
                                                        ProgressCallback callback) {
        // 检查是否有基准图
        String baselineImagePath = baselineTaskId != null ? baselineManager.getBaselineImage(baselineTaskId) : null;
        
        if (baselineImagePath != null) {
            logger.info("使用基准图: {}，基准图任务ID: {}", baselineImagePath, baselineTaskId);
            
            // 尝试从缓存获取
            if (cache.hasCache(baselineTaskId)) {
                // 从已有的基准图缓存开始处理
                logger.info("从缓存中获取基准图: {}", baselineTaskId);
                Mat baselineImage = cache.getFromCache(baselineTaskId);
                
                if (baselineImage != null) {
                    // 基于缓存的基准图处理
                    try {
                        return processImageWithParams(baselineImage, baselineImagePath, params, callback);
                    } catch (Exception e) {
                        logger.error("处理基准图时出错", e);
                        baselineImage.release(); // 确保释放资源
                    }
                }
            }
            
            // 缓存未命中或获取失败，尝试从文件加载基准图
            logger.info("从文件加载基准图: {}", baselineImagePath);
            return applyBasicAdjustments(baselineImagePath, params, callback);
        }
        
        // 没有基准图，使用原始路径
        logger.info("未找到基准图，使用原始路径: {}", inputPath);
        return applyBasicAdjustments(inputPath, params, callback);
    }
    
    /**
     * 处理带参数的图像（内部方法）
     */
    private ProcessingResult processImageWithParams(Mat inputImage, String imagePath, 
                                                   BasicAdjustment.AdjustmentParams params, 
                                                   ProgressCallback callback) {
        if (inputImage == null || inputImage.empty()) {
            logger.error("无效的输入图像");
            if (callback != null) callback.onProgress(0, "错误: 无效的输入图像");
            return null;
        }
        
        // 创建任务ID - 只基于图像路径，不再包含参数信息
        String taskId = cache.createTaskId(imagePath);
        
        logger.info("处理任务ID: {}, 参数: 亮度={}, 对比度={}, 饱和度={}, 锐化={}",
                taskId, params.getBrightness(), params.getContrast(), params.getSaturation(), params.getSharpness());
        
        // 检查缓存
        if (cache.hasCache(taskId)) {
            Mat result = cache.getFromCache(taskId);
            if (result != null) {
                logger.info("使用缓存的处理结果: {}", taskId);
                if (callback != null) callback.onProgress(100, "使用缓存图像");
                return new ProcessingResult(taskId, result);
            }
        }
        
        try {
            // 转换到RGB色彩空间以方便处理
            if (callback != null && !callback.onProgress(20, "正在处理色彩空间...")) {
                return null;
            }
            
            Mat rgbImage = new Mat();
            cvtColor(inputImage, rgbImage, COLOR_BGR2RGB);
            
            // 调整亮度和对比度
            if (callback != null && !callback.onProgress(30, "正在调整亮度和对比度...")) {
                rgbImage.release();
                return null;
            }
            
            Mat adjustedImage = adjustBrightnessContrast(rgbImage, params.getBrightness(), params.getContrast());
            rgbImage.release();
            
            // 调整饱和度
            if (callback != null && !callback.onProgress(50, "正在调整饱和度...")) {
                adjustedImage.release();
                return null;
            }
            
            Mat afterSaturation = adjustSaturation(adjustedImage, params.getSaturation());
            adjustedImage.release();
            
            // 应用锐化
            if (callback != null && !callback.onProgress(70, "正在应用锐化...")) {
                afterSaturation.release();
                return null;
            }
            
            Mat result = afterSaturation;
            if (params.getSharpness() > 0) {
                result = applySharpening(afterSaturation, params.getSharpness());
                afterSaturation.release();
            }
            
            // 转换回BGR色彩空间用于保存
            if (callback != null && !callback.onProgress(85, "正在完成处理...")) {
                result.release();
                return null;
            }
            
            Mat outputImage = new Mat();
            cvtColor(result, outputImage, COLOR_RGB2BGR);
            result.release();
            
            // 存入缓存
            cache.putToCache(taskId, outputImage.clone()); // 存储克隆以避免外部修改
            
            if (callback != null) {
                callback.onProgress(100, "图像调整完成");
            }
            
            logger.info("图像调整完成，已缓存: {}", taskId);
            return new ProcessingResult(taskId, outputImage);
            
        } catch (Exception e) {
            logger.error("处理图像时出错", e);
            if (callback != null) {
                callback.onProgress(0, "错误: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 使用任务ID和参数处理图像
     * 实现非破坏性编辑功能
     *
     * @param taskId 任务ID（前端传入，基于原图计算）
     * @param brightness 亮度参数
     * @param contrast 对比度参数
     * @param saturation 饱和度参数
     * @param sharpness 锐化度参数
     * @return 处理后的图像Mat对象
     * @throws Exception 处理失败时抛出异常
     */
    public Mat processImageWithParams(String taskId, double brightness, double contrast, 
                                     double saturation, double sharpness) throws Exception {
        logger.info("通过taskId处理图像: " + taskId + ", 参数: 亮度=" + brightness + 
                   ", 对比度=" + contrast + ", 饱和度=" + saturation + 
                   ", 锐化度=" + sharpness);
        
        // 首先尝试从缓存获取结果
        Mat cachedResult = cache.getFromCache(taskId);
        if (cachedResult != null) {
            logger.info("找到缓存的处理结果，返回缓存图像");
            return cachedResult;
        }
        
        // 读取原始图像 - 这里需要获取原图路径
        Mat originalImage = null;
        
        // 如果这是基准图对应的taskId，可以从基准图管理器获取路径
        String baselinePath = baselineManager.getBaselineImage(taskId);
        if (baselinePath != null) {
            logger.info("使用基准图路径加载图像: {}", baselinePath);
            originalImage = cache.getOrLoadSourceImage(baselinePath);
        }
        
        // 如果没有找到图像或加载失败，抛出异常
        if (originalImage == null || originalImage.empty()) {
            logger.error("无法加载任务ID对应的图像: {}", taskId);
            throw new Exception("无法加载图像，任务ID: " + taskId);
        }
        
        // 应用图像处理参数
        ImageProcessingParams params = new ImageProcessingParams();
        params.setBrightness(brightness);
        params.setContrast(contrast);
        params.setSaturation(saturation);
        params.setSharpness(sharpness);
        
        // 处理图像
        Mat processedImage = applyImageProcessing(originalImage, params);
        
        // 将处理结果存入缓存
        cache.putToCache(taskId, processedImage);
        
        return processedImage;
    }
    
    /**
     * 应用图像处理参数到输入图像
     * 
     * @param inputImage 输入图像
     * @param params 图像处理参数
     * @return 处理后的图像Mat对象
     */
    private Mat applyImageProcessing(Mat inputImage, ImageProcessingParams params) {
        logger.info("应用图像处理参数: 亮度={}, 对比度={}, 饱和度={}, 锐化={}",
                params.getBrightness(), params.getContrast(), params.getSaturation(), params.getSharpness());
        
        // 初始复制图像进行处理
        Mat result = inputImage.clone();
        Mat temp;
        
        // 如果需要，转换为RGB色彩空间以方便处理
        if (result.channels() == 3) {
            temp = new Mat();
            cvtColor(result, temp, COLOR_BGR2RGB);
            result.release();
            result = temp;
        }
        
        // 1. 调整亮度和对比度
        temp = adjustBrightnessContrast(result, params.getBrightness(), params.getContrast());
        result.release();
        result = temp;
        
        // 2. 调整饱和度
        if (Math.abs(params.getSaturation() - 1.0) > 0.01) {
            temp = adjustSaturation(result, params.getSaturation());
            result.release();
            result = temp;
        }
        
        // 3. 应用锐化
        if (params.getSharpness() > 0) {
            temp = applySharpening(result, params.getSharpness());
            result.release();
            result = temp;
        }
        
        // 如果之前转换为RGB，现在需要转回BGR
        if (result.channels() == 3) {
            temp = new Mat();
            cvtColor(result, temp, COLOR_RGB2BGR);
            result.release();
            result = temp;
        }
        
        return result;
    }
    
    /**
     * 检查taskId是否存在于缓存中
     * 
     * @param taskId 任务ID
     * @return 是否存在于缓存中
     */
    public boolean hasTaskIdInCache(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        // 直接检查缓存中是否存在此taskId
        boolean exists = cache.hasCache(taskId);
        if (exists) {
            logger.info("在缓存中找到taskId: {}", taskId);
        } else {
            logger.warn("缓存中不存在taskId: {}", taskId);
        }
        
        return exists;
    }

    /**
     * 处理点击图像事件 - 总入口
     * 处理原图、缩略图、基准图和使用图的完整流程
     * 
     * @param originalPath 原图路径
     * @param thumbnailPath 缩略图路径
     * @param taskId 任务ID
     * @param fullParams 全参数（去除当前模块的参数）- 可为null
     * @param currentModuleParams 当前模块参数 - 可为null
     * @param moduleId 当前模块ID - 可为null
     * @param callback 进度回调
     * @return 处理结果对象，包含所有需要的信息
     */
    public ImageClickResult handleImageClick(String originalPath, String thumbnailPath, String taskId,
                                         ModuleParams fullParams, 
                                         ModuleParams currentModuleParams,
                                         String moduleId, ProgressCallback callback) {
        if (originalPath == null || originalPath.trim().isEmpty()) {
            logger.error("原图路径无效");
            if (callback != null) callback.onProgress(0, "错误: 原图路径无效");
            return null;
        }
        
        try {
            // 1. 确保缩略图存在
            if (callback != null) callback.onProgress(10, "确保缩略图存在...");
            
            boolean thumbnailSuccess = ThumbnailGenerator.ensureThumbnail(originalPath, thumbnailPath);
            if (!thumbnailSuccess) {
                logger.error("生成缩略图失败: {}", originalPath);
                if (callback != null) callback.onProgress(0, "错误: 缩略图生成失败");
                return null;
            }
            
            if (callback != null) callback.onProgress(20, "缩略图准备完成");
            
            // 2. 设置为活跃图像并存储任务ID
            cache.setActiveImagePath(originalPath);
            
            // 3. 处理基准图逻辑
            String baselineTaskId = taskId;
            String baselineImagePath = null;
            
            if (fullParams == null || fullParams.isEmpty()) {
                // 无全参数，使用缩略图作为基准图
                if (callback != null) callback.onProgress(30, "使用缩略图作为基准图...");
                
                // 加载缩略图作为基准图
                Mat thumbnailImage = cache.loadImage(thumbnailPath);
                if (thumbnailImage == null) {
                    logger.error("无法加载缩略图: {}", thumbnailPath);
                    if (callback != null) callback.onProgress(0, "错误: 无法加载缩略图");
                    return null;
                }
                
                // 将缩略图存入缓存作为基准图
                cache.putToCache(baselineTaskId, thumbnailImage);
                
                // 设置为基准图TaskID
                cache.setBaselineTaskId(baselineTaskId);
                
                // 如果需要保存基准图文件
                baselineImagePath = thumbnailPath;
                
                logger.info("使用缩略图作为基准图: {}", baselineTaskId);
                
                if (callback != null) callback.onProgress(50, "基准图设置完成");
            } else {
                // 有全参数，应用参数生成基准图
                if (callback != null) callback.onProgress(30, "使用全参数生成基准图...");
                
                // 检查缓存中是否已存在基准图
                if (cache.hasCache(baselineTaskId)) {
                    logger.info("缓存中已存在基准图: {}", baselineTaskId);
                } else {
                    // 加载缩略图
                    Mat thumbnailImage = cache.loadImage(thumbnailPath);
                    if (thumbnailImage == null) {
                        logger.error("无法加载缩略图: {}", thumbnailPath);
                        if (callback != null) callback.onProgress(0, "错误: 无法加载缩略图");
                        return null;
                    }
                    
                    // 应用全参数处理生成基准图
                    if (callback != null) callback.onProgress(40, "应用全参数生成基准图...");
                    
                    // 根据参数类型处理图像
                    Mat baselineImage = applyModuleParams(thumbnailImage, fullParams);
                    thumbnailImage.release(); // 释放原缩略图
                    
                    // 将基准图存入缓存
                    cache.putToCache(baselineTaskId, baselineImage);
                    
                    // 设置为基准图TaskID
                    cache.setBaselineTaskId(baselineTaskId);
                    
                    // 如果需要保存基准图文件
                    baselineImagePath = saveToTempFile(baselineImage, "baseline_" + baselineTaskId);
                    baselineImage.release(); // 释放临时副本
                    
                    logger.info("已生成基准图: {}, 路径: {}", baselineTaskId, baselineImagePath);
                }
                
                if (callback != null) callback.onProgress(50, "基准图生成完成");
            }
            
            // 4. 处理使用图逻辑
            String useImageTaskId = baselineTaskId;
            String useImagePath = baselineImagePath;
            
            if (currentModuleParams == null || currentModuleParams.isEmpty() || moduleId == null) {
                // 无当前模块参数，使用基准图作为使用图
                if (callback != null) callback.onProgress(60, "使用基准图作为最终结果...");
                
                logger.info("使用基准图作为最终结果: {}", useImageTaskId);
                
                if (callback != null) callback.onProgress(100, "处理完成");
            } else {
                // 有当前模块参数，在基准图上应用参数生成使用图
                if (callback != null) callback.onProgress(60, "在基准图上应用当前模块参数...");
                
                // 生成使用图任务ID - 使用moduleId来区分不同功能处理的结果
                useImageTaskId = taskId + "_" + moduleId;
                
                // 检查缓存中是否已存在使用图
                if (cache.hasCache(useImageTaskId)) {
                    logger.info("缓存中已存在使用图: {}", useImageTaskId);
                    
                    // 从缓存获取使用图
                    Mat useImage = cache.getFromCache(useImageTaskId);
                    if (useImage != null) {
                        // 保存为临时文件
                        useImagePath = saveToTempFile(useImage, "use_" + useImageTaskId);
                        useImage.release(); // 释放临时副本
                    }
                } else {
                    // 从缓存获取基准图
                    Mat baselineImage = cache.getFromCache(baselineTaskId);
                    if (baselineImage == null) {
                        logger.error("无法从缓存获取基准图: {}", baselineTaskId);
                        if (callback != null) callback.onProgress(0, "错误: 无法获取基准图");
                        return null;
                    }
                    
                    // 应用当前模块参数处理生成使用图
                    if (callback != null) callback.onProgress(70, "应用当前模块参数...");
                    
                    // 根据参数类型处理图像
                    Mat useImage = applyModuleParams(baselineImage, currentModuleParams);
                    baselineImage.release(); // 释放基准图副本
                    
                    // 将使用图存入缓存
                    cache.putToCache(useImageTaskId, useImage);
                    
                    // 保存为临时文件
                    useImagePath = saveToTempFile(useImage, "use_" + useImageTaskId);
                    useImage.release(); // 释放临时副本
                    
                    logger.info("已生成使用图: {}, 路径: {}", useImageTaskId, useImagePath);
                }
                
                if (callback != null) callback.onProgress(100, "处理完成");
            }
            
            // 5. 创建并返回结果对象
            ImageClickResult result = new ImageClickResult();
            result.originalPath = originalPath;
            result.thumbnailPath = thumbnailPath;
            result.taskId = taskId;
            result.baselineTaskId = baselineTaskId;
            result.baselineImagePath = baselineImagePath;
            result.useImageTaskId = useImageTaskId;
            result.useImagePath = useImagePath;
            
            logger.info("图像点击处理完成: taskId={}, 基准图={}, 使用图={}", 
                       taskId, baselineTaskId, useImageTaskId);
            
            return result;
            
        } catch (Exception e) {
            logger.error("处理点击图像时出错", e);
            if (callback != null) {
                callback.onProgress(0, "错误: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 应用模块参数处理图像
     * 
     * @param inputImage 输入图像
     * @param params 模块参数
     * @return 处理后的图像
     */
    private Mat applyModuleParams(Mat inputImage, ModuleParams params) {
        if (params == null || inputImage == null || inputImage.empty()) {
            return inputImage.clone();
        }
        
        // 克隆输入图像，避免修改原图
        Mat outputImage = inputImage.clone();
        
        try {
            String paramType = params.getParamType();
            logger.debug("应用模块参数: 类型={}", paramType);
            
            switch (paramType) {
                case "basic_adjustment":
                    if (params instanceof BasicAdjustment.AdjustmentParams) {
                        BasicAdjustment.AdjustmentParams adjParams = (BasicAdjustment.AdjustmentParams) params;
                        // 转换到RGB色彩空间
                        Mat rgbImage = new Mat();
                        cvtColor(outputImage, rgbImage, COLOR_BGR2RGB);
                        outputImage.release();
                        
                        // 应用各种调整
                        Mat processed = applyParams(rgbImage, adjParams);
                        rgbImage.release();
                        
                        return processed;
                    }
                    break;
                    
                case "crop":
                    if (params instanceof CropParams) {
                        CropParams cropParams = (CropParams) params;
                        
                        // 应用剪裁
                        // 实际应用中需要实现裁剪逻辑，这里简化处理
                        logger.debug("应用裁剪参数: x={}, y={}, width={}, height={}, rotation={}",
                                  cropParams.getX(), cropParams.getY(), 
                                  cropParams.getWidth(), cropParams.getHeight(),
                                  cropParams.getRotation());
                        
                        // 这里应该有实际的裁剪逻辑实现
                        // ...
                    }
                    break;
                    
                default:
                    logger.warn("未知的参数类型: {}", paramType);
                    break;
            }
            
            return outputImage;
        } catch (Exception e) {
            logger.error("应用模块参数处理图像时出错", e);
            outputImage.release();
            return inputImage.clone();
        }
    }

    
    /**
     * 将Mat保存为临时文件
     */
    private String saveToTempFile(Mat image, String prefix) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile(prefix, ".jpg");
            String path = tempFile.getAbsolutePath();
            
            // 设置JPEG压缩质量
            int[] params = new int[] { opencv_imgcodecs.IMWRITE_JPEG_QUALITY, 95 };
            
            // 保存图像
            imwrite(path, image, params);
            
            // 确保文件在程序退出时删除
            tempFile.deleteOnExit();
            
            logger.debug("已保存临时图像文件: {}", path);
            return path;
        } catch (Exception e) {
            logger.error("保存临时文件时出错", e);
            return null;
        }
    }
    
    /**
     * 应用图像处理参数
     */
    private Mat applyParams(Mat inputImage, BasicAdjustment.AdjustmentParams params) {
        logger.debug("应用图像处理参数: 亮度={}, 对比度={}, 饱和度={}, 锐化={}",
                   params.getBrightness(), params.getContrast(), params.getSaturation(), params.getSharpness());
        
        // 转换到RGB色彩空间以方便处理
        Mat rgbImage = new Mat();
        cvtColor(inputImage, rgbImage, COLOR_BGR2RGB);
        
        // 调整亮度和对比度
        Mat adjustedImage = adjustBrightnessContrast(rgbImage, params.getBrightness(), params.getContrast());
        rgbImage.release();
        
        // 调整饱和度
        Mat afterSaturation = adjustSaturation(adjustedImage, params.getSaturation());
        adjustedImage.release();
        
        // 应用锐化
        Mat result = afterSaturation;
        if (params.getSharpness() > 0) {
            result = applySharpening(afterSaturation, params.getSharpness());
            afterSaturation.release();
        }
        
        // 转换回BGR色彩空间
        Mat outputImage = new Mat();
        cvtColor(result, outputImage, COLOR_RGB2BGR);
        result.release();
        
        return outputImage;
    }
    
    /**
     * 图像点击结果类
     */
    public static class ImageClickResult {
        // 输入参数
        public String originalPath;       // 原图路径
        public String thumbnailPath;      // 缩略图路径
        public String taskId;             // 任务ID
        
        // 基准图信息
        public String baselineTaskId;     // 基准图taskId
        public String baselineImagePath;  // 基准图存储路径
        
        // 使用图信息
        public String useImageTaskId;     // 使用图taskId
        public String useImagePath;       // 使用图存储路径
    }

    /**
     * 裁剪
     * @param inputPath
     * @param cropParams
     * @param callback
     * @param taskId
     * @return
     */
    public ProcessingResult applyCrop(String inputPath, CropParams cropParams, ProgressCallback callback, String taskId,boolean useTaskId) {
        if ((inputPath == null && (taskId == null || taskId.isEmpty())) || cropParams == null) {
            logger.error("输入参数无效: inputPath 和 taskId 均为空，或 cropParams 为空");
            if (callback != null) callback.onProgress(0, "错误: 输入参数无效");
            return null;
        }

        Mat src = null;
        // 优先用 taskId 从缓存读取
        if (taskId != null && !taskId.isEmpty() && useTaskId) {
            src = cache.getFromCache(taskId);
        }
        // 如果缓存没有，再用 inputPath 读取
        if ((src == null || src.empty()) && inputPath != null && !useTaskId) {
            src = cache.getOrLoadSourceImage(inputPath);
        }
        if (src == null || src.empty()) {
            logger.error("无法读取源图像: {}", inputPath);
            if (callback != null) callback.onProgress(100, "图片读取失败");
            return null;
        }

        try {
            if (callback != null && !callback.onProgress(30, "正在裁剪图片...")) {
                src.release();
                return null;
            }

            // 确保裁剪区域在图像范围内
            int x = cropParams.getX();
            int y = cropParams.getY();
            int width = cropParams.getWidth();
            int height = cropParams.getHeight();

            // 边界检查
            x = Math.max(0, x);
            y = Math.max(0, y);
            width = Math.min(width, src.cols() - x);
            height = Math.min(height, src.rows() - y);

            if (width <= 0 || height <= 0) {
                logger.error("无效的裁剪区域: x={}, y={}, width={}, height={}", x, y, width, height);
                if (callback != null) callback.onProgress(100, "裁剪区域无效");
                return null;
            }

            org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(x, y, width, height);
            Mat cropped = new Mat(src, rect); // OpenCV 会处理子图像
            
            Mat resultMat = new Mat();
            cropped.copyTo(resultMat); // 复制到新的 Mat 以确保独立所有权
            
            cropped.release(); // 释放临时裁剪的 Mat

            if (callback != null && !callback.onProgress(80, "裁剪完成，准备返回结果...")) {
                resultMat.release(); // 如果回调取消，则释放
                return null;
            }

            // 用 taskId 缓存结果
            if (taskId != null && !taskId.isEmpty()) {
                cache.putToCache(taskId, resultMat);
            }

            if (callback != null) callback.onProgress(100, "处理完成");
            
            logger.info("图像裁剪完成，已缓存: {}", taskId);
            return new ProcessingResult(taskId, resultMat.clone()); // 返回克隆以防止外部修改
        } catch (Exception e) {
            logger.error("执行裁剪任务时出错: {}", e.getMessage(), e);
            if (callback != null) { // 仅当回调非空时调用
                callback.onProgress(100, "裁剪失败: " + e.getMessage());
            }
            return null;
        } finally {
            if (src != null) {
                src.release(); // 释放源图像 Mat
            }
        }
    }
    
    /**
     * 旋转
     * @param inputPath
     * @param angle 旋转角度（度，正为逆时针）
     * @param callback
     * @param taskId
     * @return
     */
    public ProcessingResult applyRotateImage(String inputPath, int angle, ProgressCallback callback, String taskId) {
        if ((inputPath == null && (taskId == null || taskId.isEmpty()))) {
            logger.error("输入参数无效: inputPath 和 taskId 均为空");
            if (callback != null) callback.onProgress(0, "错误: 输入参数无效");
            return null;
        }

        Mat src = null;
        // 优先用 taskId 从缓存读取
        if (taskId != null && !taskId.isEmpty()) {
            src = cache.getFromCache(taskId);
        }
        // 如果缓存没有，再用 inputPath 读取
        if ((src == null || src.empty()) && inputPath != null) {
            src = cache.getOrLoadSourceImage(inputPath);
        }
        if (src == null || src.empty()) {
            logger.error("无法读取源图像: {}", inputPath);
            if (callback != null) callback.onProgress(100, "图片读取失败");
            return null;
        }

        try {
            if (callback != null && !callback.onProgress(30, "正在旋转图片...")) {
                src.release();
                return null;
            }

            // 旋转角度为0，直接返回原图
            if (angle % 360 == 0) {
                if (callback != null) callback.onProgress(100, "无需旋转");
                return new ProcessingResult(taskId, src.clone());
            }

            // 自动扩展画布，保证内容完整
            double radians = Math.toRadians(angle);
            int w = src.cols();
            int h = src.rows();
            double absCos = Math.abs(Math.cos(radians));
            double absSin = Math.abs(Math.sin(radians));
            int newW = (int) Math.round(w * absCos + h * absSin);
            int newH = (int) Math.round(w * absSin + h * absCos);

            Point2f center = new Point2f(w / 2.0f, h / 2.0f);
            Mat rotationMatrix = opencv_imgproc.getRotationMatrix2D(center, angle, 1.0);
            // 用 indexer 修改仿射矩阵的平移分量
            DoubleRawIndexer indexer = rotationMatrix.createIndexer();
            indexer.put(0, 2, indexer.get(0, 2) + (newW - w) / 2.0);
            indexer.put(1, 2, indexer.get(1, 2) + (newH - h) / 2.0);
            indexer.release();

            Mat resultMat = new Mat();
            opencv_imgproc.warpAffine(src, resultMat, rotationMatrix, new Size(newW, newH), opencv_imgproc.INTER_LINEAR, opencv_core.BORDER_CONSTANT, new Scalar(0,0,0,0));
            rotationMatrix.release();

            if (callback != null && !callback.onProgress(80, "旋转完成，准备返回结果...")) {
                resultMat.release();
                return null;
            }

            // 用 taskId 缓存结果
            if (taskId != null && !taskId.isEmpty()) {
                cache.putToCache(taskId, resultMat);
            }

            if (callback != null) callback.onProgress(100, "处理完成");

            logger.info("图像旋转完成，已缓存: {}", taskId);
            return new ProcessingResult(taskId, resultMat.clone());
        } catch (Exception e) {
            logger.error("执行旋转任务时出错: {}", e.getMessage(), e);
            if (callback != null) {
                callback.onProgress(100, "旋转失败: " + e.getMessage());
            }
            return null;
        } finally {
            if (src != null) {
                src.release();
            }
        }
    }

    // 辅助方法，用于为处理后的图像生成唯一的任务ID
    // 这假设 inputPath 是原始/基准图像的唯一标识符
    private String generateProcessedImageTaskId(String inputPath, CropParams params) {
        // 为了简单起见，组合 inputPath 和裁剪参数。
        // 在生产系统中，可以考虑更健壮的哈希或ID生成策略。
        return inputPath + "_crop_" + params.getX() + "_" + params.getY() + "_" + params.getWidth() + "_" + params.getHeight();
    }
} 