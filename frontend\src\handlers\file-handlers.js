/**
 * 文件处理程序
 * 负责文件的选择、读取和保存
 */

import { ipcMain, dialog } from 'electron';
import { promises as fs } from 'node:fs';
import fs_sync from 'node:fs';
import path from 'node:path';
import crypto from 'node:crypto';
import { store } from '../config/store-config.js';
import { calculateThumbnailPath, findImageFiles } from '../services/thumbnail-service.js';
import {sendLoadImage, startFolderScan, sendBasicAdjustParams, sendApplyImageEdit} from '../services/image-service.js';
import historyService from '../services/history-service.js';
import baseLineService from '../services/baseline-service.js';

// 主窗口引用
let mainWindow = null;

/**
 * 设置主窗口引用
 * @param {Electron.BrowserWindow} window 主窗口对象
 */
export function setMainWindow(window) {
  mainWindow = window;
}

/**
 * 注册文件处理相关的IPC处理程序
 */
export function registerFileHandlers() {
  // 处理文件选择对话框
  ipcMain.handle('select-files', async () => {
    if (!mainWindow) return [];
    
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'png', 'gif', 'bmp', 'jpeg', 'tiff'] }
      ]
    });
    
    return result.filePaths;
  });

  // 处理文件夹选择对话框
  ipcMain.handle('select-directory', async () => {
    if (!mainWindow) {
      return { success: false, error: '主窗口不存在' };
    }

    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
        title: '选择文件夹'
      });

      if (!result.canceled && result.filePaths.length > 0) {
        return { success: true, directory: result.filePaths[0] };
      } else {
        return { success: false, error: '用户取消了选择' };
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取缩略图路径
  ipcMain.handle('get-thumbnail-path', async (_event, data) => {
    const { filePath } = data;
    
    if (!filePath) {
      return { success: false, error: '参数不完整' };
    }
    
    try {
      const result = await calculateThumbnailPath(filePath);
      
      if (result.success) {
        return { 
          success: true, 
          thumbnailPath: result.thumbnailPath,
          thumbnailExists: result.thumbnailExists
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('获取缩略图路径失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 计算缩略图路径
  ipcMain.handle('calculate-thumbnail-path', async (_event, data) => {
    return await calculateThumbnailPath(data.filePath);
  });

  // 列出目录中的图像文件
  ipcMain.handle('list-images', async (_event, data) => {
    const { params } = data;
    const { directoryPath } = params;
    
    try {
      // 检查目录是否存在
      if (!fs_sync.existsSync(directoryPath)) {
        return { success: false, error: '目录不存在' };
      }

      // 读取目录内容
      const files = await fs.readdir(directoryPath, { withFileTypes: true });
      
      // 过滤出图像文件
      const imageFiles = files
        .filter(file => {
          if (file.isDirectory()) return true;
          const ext = path.extname(file.name).toLowerCase();
          return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'].includes(ext);
        })
        .map(file => ({
          name: file.name,
          path: path.join(directoryPath, file.name),
          type: file.isDirectory() ? 'directory' : 'file'
        }));

      return {
        success: true,
        data: {
          images: imageFiles
        }
      };
    } catch (error) {
      console.error('列出图像失败:', error);
      return { success: false, error: '无法列出目录内容' };
    }
  });

  // 加载图像操作历史
  ipcMain.handle('load-image-history', async (_event, data) => {
    const { filePath } = data;
    
    if (!filePath) {
      return { success: false, error: '参数不完整' };
    }
    
    try {
      // 首先计算缩略图路径，历史文件以缩略图文件为基础命名
      const thumbnailResult = await calculateThumbnailPath(filePath);
      
      if (!thumbnailResult.success) {
        return { success: false, error: '无法确定缩略图路径' };
      }
      
      // 构建历史文件路径（替换后缀为.history.json）
      const historyFilePath = thumbnailResult.thumbnailPath.replace(/\.[^\.]+$/, '.history.json');
      
      // 检查历史文件是否存在
      if (!fs_sync.existsSync(historyFilePath)) {
        return { success: false, error: '历史文件不存在', data: { operationStack: [], redoStack: [] } };
      }
      
      // 读取历史文件
      const historyContent = await fs.readFile(historyFilePath, 'utf8');
      const historyData = JSON.parse(historyContent);
      console.log(`已加载图像历史数据，共 ${historyData.operationStack?.length || 0} 个操作`);
      
      return {
        success: true,
        data: historyData
      };
    } catch (error) {
      console.error('加载图像历史失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        data: { operationStack: [], redoStack: [] } // 返回空数据以避免前端错误
      };
    }
  });



  // 加载图像参数
  ipcMain.handle('load-image-params', async (_event, data) => {
    const { filePath } = data;

    if (!filePath) {
      return { success: false, error: '参数不完整' };
    }

    try {
      // 获取当前模块的最新参数
     // const currentModuleParams = historyService.getCurrentModuleParams();

      // 获取其他模块的参数，用于构建基准图
      const otherModuleParams = historyService.getOtherModuleParams();

      console.log(`其他模块参数`,otherModuleParams);

      return {
        success: true,
        data: otherModuleParams
      };
    } catch (error) {
      console.error('加载图像历史失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        data: { operationStack: [], redoStack: [] } // 返回空数据以避免前端错误
      };
    }
  });

  // 模块处理
  ipcMain.handle('process-module', async (_event, data) => {
    try {
      const result = await sendLoadImage(data);
      console.log('Received response:', result);
      return result;
    } catch (error) {
      console.error('Error receiving response:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });


  // 模块处理
  ipcMain.handle('basic_adjust_params', async (_event, data) => {
    try {
      const result = await sendBasicAdjustParams(data);
      console.log('Received response:', result);
      return result;
    } catch (error) {
      console.error('Error receiving response:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });



  // 模块处理
  ipcMain.handle('apply-image-edit', async (_event, data) => {
    try {
      const result = await sendApplyImageEdit(data);
      console.log('Received response:', result);
      return result;
    } catch (error) {
      console.error('Error receiving response:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });


  // 保存图像操作历史
  ipcMain.handle('save-image-history', async (_event, data) => {
    const { filePath, operationStack, redoStack, finalState } = data;
    
    if (!filePath) {
      console.error('保存历史失败: 未提供文件路径');
      return { success: false, error: '参数不完整' };
    }
    
    try {
      console.log('保存图像历史 - 开始处理:', { 
        filePath, 
        operationCount: operationStack?.length || 0,
        redoCount: redoStack?.length || 0,
        hasFinalState: !!finalState
      });
      
      // 首先计算缩略图路径
      const thumbnailResult = await calculateThumbnailPath(filePath);
      
      if (!thumbnailResult.success) {
        console.error('保存历史失败: 无法计算缩略图路径:', thumbnailResult.error);
        return { success: false, error: '无法确定缩略图路径' };
      }
      
      // 构建历史文件路径
      const historyFilePath = thumbnailResult.thumbnailPath.replace(/\.[^\.]+$/, '.history.json');
      console.log('历史文件路径:', historyFilePath);
      
      // 确保目录存在
      const dirPath = path.dirname(historyFilePath);
      try {
        console.log('尝试创建目录:', dirPath);
        await fs.mkdir(dirPath, { recursive: true });
        console.log('目录创建成功或已存在');
        
        // 检查目录是否可写
        try {
          await fs.access(dirPath, fs_sync.constants.W_OK);
          console.log('目录可写');
        } catch (accessError) {
          console.error('目录访问权限问题:', accessError);
          return { success: false, error: '无权限写入目录: ' + dirPath };
        }
      } catch (mkdirError) {
        console.error('创建目录失败:', mkdirError);
        return { success: false, error: '无法创建目录: ' + dirPath };
      }
      
      // 准备历史数据
      const historyData = {
        imageId: thumbnailResult.fileHash,
        originalPath: filePath,
        thumbnailPath: thumbnailResult.thumbnailPath,
        lastModified: Date.now(),
        operationStack: operationStack || [],
        redoStack: redoStack || [],
        finalState: finalState || null // 新增：保存最终状态
      };
      
      // 写入历史文件
      const historyContent = JSON.stringify(historyData, null, 2);
      console.log(`准备写入历史文件 (${historyContent.length} 字节)`);
      
      try {
        await fs.writeFile(historyFilePath, historyContent, 'utf8');
        
        // 验证文件是否已写入
        try {
          const stats = await fs.stat(historyFilePath);
          console.log(`文件写入成功: ${historyFilePath}, 大小: ${stats.size} 字节`);
        } catch (statError) {
          console.error('文件写入后无法验证:', statError);
        }
      } catch (writeError) {
        console.error('写入历史文件失败:', writeError);
        return { success: false, error: '写入文件失败: ' + writeError.message };
      }
      
      console.log(`已保存图像历史数据，共 ${historyData.operationStack.length} 个操作${finalState ? '，包含最终状态' : ''}`);
      
      return {
        success: true,
        historyFilePath
      };
    } catch (error) {
      console.error('保存图像历史失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
}