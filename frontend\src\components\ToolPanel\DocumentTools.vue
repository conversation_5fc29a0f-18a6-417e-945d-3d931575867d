<template>
  <div class="tool-group">
    <div class="document-tools">
      <el-button 
        type="primary" 
        @click="detectBoundaries"
      >
        检测边界
      </el-button>
      
      <el-button 
        type="primary" 
        @click="perspectiveCorrection"
      >
        透视校正
      </el-button>
      
      <div class="tool-item">
        <div class="tool-header">
          <span>去污</span>
          <span class="param-value">{{ params.denoise || 0 }}</span>
        </div>
        <el-slider 
          v-model="localParams.denoise" 
          :min="0" 
          :max="100" 
          :step="1"
          @change="updateParams"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, onMounted } from 'vue';

const props = defineProps({
  params: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update-params', 'detect-boundaries', 'perspective-correction']);

// 本地参数副本
const localParams = reactive({
  denoise: 0
});

// 初始化本地参数
onMounted(() => {
  console.log('DocumentTools 组件挂载，初始参数:', props.params);
  if (props.params.denoise !== undefined) {
    localParams.denoise = props.params.denoise;
  }
});

// 监听props变化
watch(() => props.params, (newParams) => {
  console.log('DocumentTools: 接收到新参数:', newParams);
  if (newParams.denoise !== undefined) {
    localParams.denoise = newParams.denoise;
    console.log(`更新去污参数: ${newParams.denoise}`);
  }
}, { deep: true, immediate: true });

// 更新参数
const updateParams = () => {
  emit('update-params', { denoise: localParams.denoise });
};

// 检测边界
const detectBoundaries = () => {
  emit('detect-boundaries');
};

// 透视校正
const perspectiveCorrection = () => {
  emit('perspective-correction');
};
</script>

<style scoped>
.tool-group {
  padding: 5px 0;
}

.document-tools {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tool-item {
  margin-top: 10px;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--text-color);
}

.param-value {
  color: var(--text-color-light);
}
</style> 