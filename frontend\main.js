"use strict";
// 引入基础模块
import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// 计算 __dirname 和 __filename (ESM中不直接可用)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 引入配置模块
import { initializeSettings } from './src/config/store-config.js';
import { WS_CONFIG } from './src/config/app-config.js';

// 引入服务模块
import { restartBackendService } from './src/services/backend-service.js';
import { connectToWebSocket, setMainWindow as setWebSocketMainWindow } from './src/services/websocket-service.js';
import { setMainWindow as setImageServiceMainWindow } from './src/services/image-service.js';

// 引入处理程序模块
import { registerSettingsHandlers } from './src/handlers/settings-handlers.js';
import { setMainWindow as setFileHandlersMainWindow, registerFileHandlers } from './src/handlers/file-handlers.js';
import { registerImageHandlers } from './src/handlers/image-handlers.js';

// 初始化应用设置
initializeSettings();

// 保持对window对象的全局引用，避免JavaScript对象被垃圾回收时，窗口被自动关闭
let mainWindow = null;

/**
 * 创建主窗口
 */
function createWindow() {
    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        fullscreen: false, // 不使用全屏
        maximizable: true, // 允许最大化
        webPreferences: {
            nodeIntegration: false, // 关闭Node.js集成，增强安全性
            contextIsolation: true, // 启用上下文隔离
            webSecurity: false,     // 关闭web安全策略，允许加载本地资源
            preload: path.join(__dirname, 'preload.js')
        },
        title: '图像处理工具' // 设置窗口标题为中文
    });
    
    // 隐藏菜单栏
    mainWindow.setMenuBarVisibility(false);
    
    // 最大化窗口
    mainWindow.maximize();

    // 设置窗口引用到各个服务和处理程序
    setWebSocketMainWindow(mainWindow);
    setImageServiceMainWindow(mainWindow);
    setFileHandlersMainWindow(mainWindow);
    
    // 设置全局窗口引用，便于某些函数访问
    global.mainWindow = mainWindow;
    
    // 加载应用
    // 设置开发环境
    process.env.NODE_ENV = 'development';
    const startUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:3000' // 更新为当前Vite实际端口
        : `file://${path.join(__dirname, 'dist/index.html')}`;
    console.log('Loading URL:', startUrl);
    mainWindow.loadURL(startUrl);
    
    // 打开开发者工具
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
    
    // 确保标题设置正确
    mainWindow.on('page-title-updated', (event) => {
        event.preventDefault(); // 阻止页面标题更新
        mainWindow.setTitle('图像处理工具'); // 保持窗口标题为中文
    });
    
    // 当window被关闭时，触发事件
    mainWindow.on('closed', function () {
        mainWindow = null;
        global.mainWindow = null;
    });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(createWindow);

// 所有窗口关闭时退出应用
app.on('window-all-closed', function () {
    if (process.platform !== 'darwin')
        app.quit();
});

app.on('activate', function () {
    if (mainWindow === null)
        createWindow();
});

// 注册所有IPC处理程序
registerSettingsHandlers();
registerFileHandlers();
registerImageHandlers();

// 监听应用启动完成事件
app.on('ready', () => {
    // 确保后端服务已启动
    restartBackendService();
    
    // 等待服务启动后连接
    setTimeout(connectToWebSocket, 2000);
}); 