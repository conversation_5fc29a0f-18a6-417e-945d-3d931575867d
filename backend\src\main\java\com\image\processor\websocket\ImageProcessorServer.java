package com.image.processor.websocket;

import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.image.processor.websocket.handlers.*;
import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * WebSocket服务器，处理图像处理请求
 */
public class ImageProcessorServer extends WebSocketServer {
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessorServer.class);
    
    // 线程池用于处理图像处理任务
    private final ExecutorService taskExecutor = Executors.newFixedThreadPool(
        Math.max(2, Runtime.getRuntime().availableProcessors())
    );
    
    // 存储活动任务
    private final Map<String, TaskInfo> activeTasks = new ConcurrentHashMap<>();
    
    // 处理器映射
    private final Map<String, RequestHandler> handlers = new HashMap<>();
    
    public ImageProcessorServer(InetSocketAddress address) {
        super(address);
        registerHandlers();
    }
    
    /**
     * 注册所有请求处理器
     */
    private void registerHandlers() {
        // 注册各种处理器
        addHandler(new GenerateThumbnailHandler(taskExecutor, activeTasks));
        addHandler(new ProcessDirectoryHandler(taskExecutor, activeTasks));
        addHandler(new AdjustParamsHandler(taskExecutor, activeTasks));
        addHandler(new GetCachedImageHandler(taskExecutor));
        addHandler(new SetBaselineImageHandler(taskExecutor, activeTasks));
        addHandler(new CancelTaskHandler(taskExecutor, activeTasks));
        addHandler(new RotateImageHandler(taskExecutor, activeTasks));
        addHandler(new HandleImageClickHandler(taskExecutor, activeTasks));
        addHandler(new CropImageHandler(taskExecutor, activeTasks));
        addHandler(new GetCachedImageApplyParamsHandler(taskExecutor));

        logger.info("已注册 {} 个请求处理器", handlers.size());
    }
    
    /**
     * 添加请求处理器
     */
    private void addHandler(RequestHandler handler) {
        handlers.put(handler.getActionName(), handler);
        logger.debug("已注册处理器: {}", handler.getActionName());
    }
    
    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        String clientId = getClientId(conn);
        logger.info("新连接：{} ({})", clientId, conn.getRemoteSocketAddress());
        
        // 发送欢迎消息
        JSONObject welcomeMsg = new JSONObject();
        try {
            welcomeMsg.put("type", "connected");
            welcomeMsg.put("message", "已连接到图像处理服务");
            welcomeMsg.put("clientId", clientId);
            conn.send(welcomeMsg.toString());
        } catch (JSONException e) {
            logger.error("创建欢迎消息时出错", e);
        }
    }
    
    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        String clientId = getClientId(conn);
        logger.info("连接关闭：{} ({}), 代码: {}, 原因: {}, 远程关闭: {}", 
                   clientId, conn.getRemoteSocketAddress(), code, reason, remote);
        
        // 取消该连接的所有任务
        activeTasks.entrySet().removeIf(entry -> {
            TaskInfo taskInfo = entry.getValue();
            if (taskInfo.getConn() == conn) {
                taskInfo.cancel();
                logger.info("取消任务: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    @Override
    public void onMessage(WebSocket conn, String message) {
        String clientId = getClientId(conn);
        logger.debug("收到消息：{} ({}): {}", clientId, conn.getRemoteSocketAddress(), message);
        
        try {
            JSONObject request = new JSONObject(message);
            String action = request.getString("action");
            logger.debug("解析到action: {}", action);
            
            // 查找处理器并处理请求
            RequestHandler handler = handlers.get(action);
            if (handler != null) {
                logger.debug("使用处理器 {} 处理请求", handler.getClass().getSimpleName());
                handler.handle(conn, request);
            } else {
                logger.error("未知的操作: {}", action);
                sendError(conn, "未知的操作: " + action);
            }
        } catch (JSONException e) {
            logger.error("解析消息时出错", e);
            sendError(conn, "无效的JSON格式: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理消息时出错", e);
            sendError(conn, "处理请求时出错: " + e.getMessage());
        }
    }
    
    @Override
    public void onError(WebSocket conn, Exception ex) {
        String clientId = conn != null ? getClientId(conn) : "未知";
        String address = conn != null ? conn.getRemoteSocketAddress().toString() : "未知";
        logger.error("WebSocket错误：{} ({}): {}", clientId, address, ex.getMessage(), ex);
    }
    
    @Override
    public void onStart() {
        logger.info("WebSocket服务器已启动");
    }
    
    /**
     * 发送错误消息
     */
    private void sendError(WebSocket conn, String errorMessage) {
        try {
            JSONObject errorMsg = new JSONObject();
            errorMsg.put("type", "error");
            errorMsg.put("message", errorMessage);
            
            conn.send(errorMsg.toString());
        } catch (JSONException e) {
            logger.error("创建错误消息时出错", e);
        }
    }
    
    /**
     * 获取客户端ID
     */
    private String getClientId(WebSocket conn) {
        return conn.getRemoteSocketAddress().toString();
    }
} 