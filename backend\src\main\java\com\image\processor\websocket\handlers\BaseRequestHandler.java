package com.image.processor.websocket.handlers;

import java.util.UUID;
import java.util.concurrent.ExecutorService;

import org.java_websocket.WebSocket;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础请求处理器
 * 提供通用方法和错误处理
 */
public abstract class BaseRequestHandler implements RequestHandler {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ExecutorService taskExecutor;
    
    /**
     * 构造函数
     * 
     * @param taskExecutor 任务执行线程池
     */
    public BaseRequestHandler(ExecutorService taskExecutor) {
        this.taskExecutor = taskExecutor;
    }
    
    /**
     * 发送任务更新消息
     */
    protected void sendTaskUpdate(WebSocket conn, String taskId, String status, int progress, Object data) {
        try {
            JSONObject update = new JSONObject();
            update.put("type", "task_update");
            update.put("taskId", taskId);
            update.put("status", status);
            update.put("progress", progress);
            update.put("data", data);
            
            conn.send(update.toString());
        } catch (JSONException e) {
            logger.error("创建任务更新消息时出错", e);
        }
    }
    
    /**
     * 发送错误消息
     */
    protected void sendError(WebSocket conn, String errorMessage) {
        try {
            JSONObject errorMsg = new JSONObject();
            errorMsg.put("type", "error");
            errorMsg.put("message", errorMessage);
            
            conn.send(errorMsg.toString());
        } catch (JSONException e) {
            logger.error("创建错误消息时出错", e);
        }
    }
    
    /**
     * 获取客户端ID
     */
    protected String getClientId(WebSocket conn) {
        return conn.getRemoteSocketAddress().toString();
    }
    
    /**
     * 创建任务ID
     * 
     * @return 新的任务ID
     */
    protected String createTaskId() {
        return UUID.randomUUID().toString();
    }
} 